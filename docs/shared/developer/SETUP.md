---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# Setup DataTP Project

### Tools And Config Requirement

- Node.js (version > 23)
- pnpm
- Git
- Java (version 21)
- <PERSON><PERSON><PERSON> (version > 8.7)
- VS Code, Eclipse, ...
- Postgres Server (version 16 above)
- DBeaver - Database Tool to explore a database and table relations

##### Other tools
- Python (version 3.10 above)
- Docker
- K3s

#### Config

1. Git
```
  git config --global user.email "<EMAIL>"
  git config --global user.name "Your Name"

  git config --global core.filemode false
  #Line ending with unix style
  git config --global core.autocrlf false
```

2. SSH Key
```bash
# Tạo SSH key mới

ssh-keygen -t ed25519 -b 4096 -C "<EMAIL>"

# Khởi động SSH agent
eval "$(ssh-agent -s)"

# Thêm SSH key vào SSH agent
ssh-add ~/.ssh/id_ed25519
```

#### Thêm SSH key vào tài k<PERSON>n GitLab
- Sao chép nội dung SSH key
```bash
cat ~/.ssh/id_ed25519.pub | pbcopy
```

- Đăng nhập vào GitLab
- Vào Settings > SSH Keys
- Dán SSH key và đặt tên cho key


### Installation Projects

#### Tạo thư mục root và clone dự án

```bash
# Tạo thư mục root
mkdir datatp
cd datatp

# Clone các dự án
git clone git@gitlab:datatp.net:tuan/datatp-core.git
git clone git@gitlab:datatp.net:tuan/datatp-erp.git
git clone git@gitlab:datatp.net:tuan/datatp-document-ie.git
git clone git@gitlab:datatp.net:tuan/datatp-logistics.git
git clone git@gitlab:datatp.net:tuan/datatp-crm.git
git clone git@gitlab:datatp.net:tuan/datatp-build.git
```

#### Backend

Cài đặt dependencies và build cho từng projects theo thứ tự `datatp-core`,`datatp-erp`, `datatp-document-ie`, `datatp-logistics`,
`datatp-crm`, `datatp-build`.

```
cd datatp-core
gradle clean build -x test
gradle publishToMaven
```

#### FrontEnd

Install và build webui cho các dự án `datatp-erp`, `datatp-document-ie`, `datatp-logistics`,
`datatp-crm`, `datatp-build`.

```
cd datatp-erp/webui/lib
pnpm install
pnpm run build
```
#### Database and enviroment


#### Start the development server

Open `http://localhost:3000` to view the site.

