---
sidebar_position: 11
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# Qu<PERSON>n lý <PERSON> hàng (Pending)

Hướng dẫn quản lý thông tin khách hàng (Customer) trong hệ thống CRM.

## Tổng quan

Customer là khách hàng đã được xác thực và đang hoạt động trong hệ thống. Module này giúp:
- Qu<PERSON>n lý thông tin khách hàng tập trung
- Theo dõi lịch sử giao dịch
- <PERSON><PERSON> tích hành vi khách hàng

## <PERSON><PERSON><PERSON> bước thực hiện

### 1. Tạo mới Customer

#### Yêu cầu tạo khách hàng
- Click menu `Customers` trong sidebar
- Click `New Customer` trên thanh công cụ
- Điền đầy đủ thông tin theo form

#### Thông tin bắt buộc
- Thông tin công ty:
  - T<PERSON><PERSON> công ty (*)
  - <PERSON><PERSON> số thuế (*)
  - <PERSON><PERSON><PERSON> chỉ đăng ký (*)
- Thông tin liên hệ:
  - <PERSON><PERSON><PERSON><PERSON> đại diện (*)
  - Email (*)
  - Số điện thoại (*)
- Thông tin bổ sung:
  - Phân loại khách hàng
  - Sales phụ trách
  - Hạn mức tín dụng

> **Lưu ý**: Yêu cầu tạo khách hàng sẽ được chuyển đến BFSOne để phê duyệt

### 2. Quản lý thông tin khách hàng

#### Tìm kiếm khách hàng
- Tìm theo tên/mã số thuế
- Lọc theo trạng thái
- Lọc theo sales phụ trách
- Lọc theo phân loại

#### Cập nhật thông tin
- Yêu cầu cập nhật qua IT Support
- Đính kèm tài liệu xác thực nếu cần
- Theo dõi trạng thái yêu cầu

### 3. Theo dõi hoạt động

#### Lịch sử giao dịch
- Danh sách báo giá
- Lịch sử booking
- Công nợ và thanh toán

#### Tương tác với khách hàng
- Lịch sử liên hệ
- Ghi chú trao đổi
- Feedback và đánh giá

### 4. Báo cáo và phân tích

#### Báo cáo doanh số
- Doanh số theo thời gian
- Phân tích theo dịch vụ
- So sánh với kế hoạch

#### Phân tích khách hàng
- Tần suất sử dụng dịch vụ
- Giá trị trung bình/đơn hàng
- Tỷ lệ chuyển đổi từ báo giá

## Quyền hạn và phân quyền

### Phân quyền theo vai trò

- Admin: Toàn quyền quản lý
- Company: Quản lý Lead trong VP/Phòng ban/ Team
- User: Quản lý Lead được phân công, theo từng Saleman.

### Chính sách bảo mật
- Thông tin khách hàng được đồng bộ từ BFSOne
- Chỉ người được phân quyền mới xem được thông tin chi tiết
- Mọi thay đổi đều được ghi nhận vào lịch sử

## Demo thao tác
<!-- ![customer_management.gif](./img/customer_management.gif) -->

## Tích hợp hệ thống

### Đồng bộ dữ liệu
- Tự động đồng bộ với BFSOne
- Cập nhật thông tin theo thời gian thực
- Đồng bộ lịch sử giao dịch
