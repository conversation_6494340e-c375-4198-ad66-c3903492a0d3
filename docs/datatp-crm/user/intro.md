---
sidebar_position: 1
sidebar: false
hide_table_of_contents: true
displayed_sidebar: userSidebar
---

# Overview

Hệ thống CRM được xây dựng nhằm mục tiêu quản lý tập trung cơ sở dữ liệu, tăng năng suất và tối ưu hóa các quy trình kinh doanh trong lĩnh vực logistics.

<PERSON>ệ thống bao gồm hai module chính: **Pricing Tools** và **CRM**.

![pricing_dashboard.jpg](/img/guides/datatp-crm/pricing/pricing_dashboard.png)

#### **<PERSON><PERSON><PERSON> tiêu của hệ thống**

1. **Quản lý cơ sở dữ liệu tập trung**

Hệ thống CRM quản lý cơ sở dữ liệu về giá cho các dịch vụ **Sea/Air**, **Logistics**, **Trucking**, và **CBT**, giúp đưa ra phương án đầu tư và quản trị sản phẩm hiệu quả.

2. **Quản trị quy trình mua hàng nội bộ**

Hệ thống cung cấp các công cụ đo lường và quản lý quy trình mua hàng, bao gồm:
> - Số lượng inquiry.
> - Offer và feedback.
> - KPI của từng nhân viên và chi nhánh.

- Tăng năng suất lao động
CRM giúp giảm thời gian báo giá bằng cách lưu trữ dữ liệu giá cho các hành trình và dịch vụ thường xuyên. Các công cụ này hỗ trợ sales trong việc:
- Gửi thư ngỏ và báo giá hàng loạt.
- Chuyển đổi dữ liệu giá bán tự động sang hệ thống OF1.

3. **Kênh Sales & Marketing Online**

Hệ thống tích hợp kênh **sales và marketing online** với các tính năng:
- Từ e-quote đến booking, track & trace, và e-pay.
- Thực hiện các chiến dịch marketing như email quảng cáo, promotion.
- Thu thập phản hồi và đánh giá từ khách hàng.

#### **Module/ App nghiệp vụ**

1. **Pricing Tools**
- Dữ liệu giá dịch vụ (sea, air, logistics & CBT) được nhập liệu và kiểm duyệt bởi manager.
- Các văn phòng và đối tác có thể nhập giá trực tiếp vào hệ thống, không cần qua email.

2. **CRM**
- Sales nhập **inquiry** lên hệ thống. Nếu có sẵn giá, tool sẽ gửi tự động cho sales và khách hàng qua email.
- Nếu chưa có giá, hệ thống sẽ tự động gửi email đến bộ phận pricing hoặc văn phòng liên quan.
- Sau khi nhận giá, sales có thể tạo **quotation** và gửi tới khách hàng, có thể áp dụng cho nhiều booking.
- Khi khách hàng confirm booking, hệ thống sẽ API với OF1 để tạo booking và tự động cập nhật giá vào OF1.

3. **Dashboard**

Hệ thống cung cấp báo cáo chi tiết bao gồm:
> - Số lượng inquiry (có thể lọc theo yêu cầu).
> - Số lượng quotation và booking thành công.
> - Thống kê volume và KPI cho từng vị trí.

**Dashboard** giúp quản lý dữ liệu tập trung và hỗ trợ phân bổ, tối ưu nguồn lực công ty một cách chính xác.
```
