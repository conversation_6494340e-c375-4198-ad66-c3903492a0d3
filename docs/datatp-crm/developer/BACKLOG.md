---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# DataTP CRM Backlog

## Tasks


| Category    | Title      | Status      | Timeline   | PIC  |
| :------------ | :----------- | :------------ | :----------- | :----- |
| Feature     | Task 1     | In Progress | 2025-06-20 | Dan  |
| Bug         | Fix issue  | To Do       | 2025-06-25 | Nhat |
| Enhancement | Improve UI | Done        | 2025-06-15 | An   |

**Sprint Rules:**

- Sprint duration: 1 week (Monday-Sunday)
- Daily status updates required
- Incomplete tasks return to backlog for next sprint prioritization

## Current Sprint

_Tasks planned for current week. Incomplete tasks will return to backlog at the end of the sprint._

1. [An] #pricing - Company Pricing Dashboard:

- chức năng lọc theo Type Of Service cho toàn bộ dữ liệu, cho Top Route Performance.
- export excel từng section/ bảng.

2. [An] Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.
   -> <PERSON><PERSON><PERSON> nhật xong, viết cron để sync lại với hệ thống bee_legacy (bảo Đàn làm)

2. [An] #partner - Update Partner Data:
   - Cập nhật thông tin các trường industry_sector (từ excel)
   - Cập nhật date_created, date_modified (từ BFSOne) cho partner.
   - Viết cron để sync lại với hệ thống bee_legacy (bảo Đàn làm)

## Backlog

_Future tasks_

1. Company Pricing Dashboard:

- chức năng lọc theo Type Of Service cho toàn bộ dữ liệu, cho Top Route Performance.
- export excel từng section/ bảng.

2. Nghiên cứu, chỉnh lại shell script cho postgres-admin.sh

- Cho phép thực hiện các lệnh tạo db user, db user readonly, new db, drop db, ... cho các dự án vừa tách.

3. Nghiên cứu chuyển qua code java trên VS Code.
4. Enhance document web UI Framework.

- Nghiên cứu để tập trung docs/ guide trên từng dự án và có cách để merge, collect các docs và build lên.
- Tạo khung, template, quy ước folder chung cho từng dự án.
- Ngoài docs (cho users) , thêm các mục wiki (giải thích chức năng, nghiệp vụ, thuận ngữ để cho dev tìm hiểu phần mềm), changelog (lưu lịch sử thay đổi theo các phiên bản Released)

5. Xử lý dữ liệu partners lost cho HCM

- Dữ liệu Tâm gửi dạng excel, gồm các khách hàng đã lâu ko phát sinh booking. Y/c dev kiểm tra/ check lại với hệ thống, nếu không phát sinh booking trong 1 năm gần đây => Import vào CRM.

6. Theo dõi công việc hàng xuất.

- Mail confirm giá (giá có sẵn trên bảng giá - tính năng cho phép sales gửi mail để confirm với pricing về giá để báo lại với khách hàng )
  (Các quy trình, tham khảo hàng nhập đang làm)

7. #sale_dashboard Enhance báo cáo Saleman Activities Tracker - Mrs. Minh Sales HPH

- Cố định row header và cột đầu tiên.
- Kiểm tra lại chỉ số Khách hàng mới/ lead mới.
- Thêm cột số quotation được tạo theo từng sales.
- Sắp xếp thứ tự các cột

8. Review toàn bộ query trên module CRM (chuẩn bị cho việc tách database)
9. Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.
   -> Cập nhật xong, viết cron để sync lại với hệ thống bee_legacy (bảo Đàn làm)
10. #sale_daily_tasks: [Mrs Minh Sale HPH] - create xong, thì a k tich vào cái send zalo được nữa.
    Enhance: cho phép sales set/ chỉnh giờ thông báo sau khi tasks được tạo. Hiện tại chỉ set được lúc tạo, tạo xong bị lock và không cho set.
    Mỗi khi update tasks, check notification time nếu > now thì schedule message để gửi cho người dùng.
