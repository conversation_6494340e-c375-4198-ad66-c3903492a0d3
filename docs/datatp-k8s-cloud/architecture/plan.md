# Server Hardware and Plan Requirements 

### A mini kubernetes cluster hardware requirements:

-  1 Firewall Router
-  At least 3 barebone servers 
-  Dual CPU and at least 10 cores each cpu
-  256GB RAM
-  1 TB storage SSD
-  1 TB 5GB storage HHD
-  10GB network 

Budgets

| Hardware Name   |      Description         | Cost  |
|-----------------|:------------------------:|------:|
| Frame           |                          | $1000 |
| Router          |                          | $1000 |
| Server 1        |Chasis, CPU, SSD, HHD, RAM| $1000 |
| Server 1        |Chasis, CPU, SSD, HHD, RAM| $1000 |
| Server 1        |Chasis, CPU, SSD, HHD, RAM| $1000 |
| Others          |Cables..                  | $1000 |
| Total           |                          | $1000 |
  
### Server name and configuration

- server should be named as k8s-node-001, k82-node-002... where the first 2 or 3 nodes should be the master and the rest are the workers.
- Usually the master node should be portected and have the best hardware equipment for the reliability.
- The node should be labled k8s-master and k8s-worker. In case a node act as both master and worker, it should have both labels k8s-master and k8s-worker.
- The node may have other labels, such as k8s-db-worker or k8s-io-worker, which indicate that it has high-performance I/O disk resources reserved.
- The k8s node should use the class A or B ip range from 172.16.xxx.xxx/16 or 10.xxx.xxx.xxx/8

### Storage configuration
- Should discuss about volume, partition, raid technology
- os-disk: Use to install os, boot, the required service such containerd, rancher service... Need 5GB - 10GB
- hp-io-disk: Is reserved for the high-performance I/O disk, should be from 0GB - 500GB
  + Raid 0: have the best performance, size but not reliable. the configuration will loose the data if there is one disk failure
  + Raid 5: Have the good performance and can support one disk failure
- ceph-hp is a high performance(use SSD), reliable, replcated at leasr on 3 nodes.. should be from 500GB - 5TB
- ceph is large(use disk), reliable, replcated at leasr on 3 nodes.. should be from 1TB - 5TB

Storage performance test
- Should run read/write performance test on os-disk, hp-io-disk, ceph-hp, ceph

Run write performance test
```
dd if=/dev/zero of=/mnt/pve/cephfs/test1.img bs=1G count=1 oflag=direct
```

| Machine         | Transfer          |
|-----------------|:-----------------:|
| Cluster Ceph    |           38 MB/s |
| Cluster HDD     |          240 MB/s |
| Techcity VM     |      46 - 92 MB/s |
| Mac M1          |  1516 - 2000 MB/s |
| SATA HHD Theory |      500-600 MB/s |
| SSD Theory      |  3,000-7,000 MB/s |


# Kubernetes Plan And Requirements

Namespace, container name and vm name configuration
- The namespace should follow the convention {projectCode}-{env}, for example:
  +  datatp-prod, 
  +  datatp-dev, datatp-dev-1, datatp-dev-2
  +  datatp-local
  +  beehph-sale-vm, beehph-cus-vm
- The container name should follow the convention ${appName}, for exampple:
  + postgres, redis
  + datatp-core, datatp-crm, datatp-tms, datatp-document-ie
- The vm name should follow the convention {distributor}-{role}-{id}, for exampple:
  + win11-sale-001, win11-sale-002...
  + win11-cus-001, win11-cus-002...
  + debian12-k8s-rancher-0001
  + debian12-k8s-master-001, debian12-k8s-master-002 
  + debian12-k8s-worker-001, debian12-k8s-worker-002 

# Setup Servers

IP Allocations:
- ************   - 20  => For devices such  firewall, wifi , printer....
- *************  - 50  => For k8s-node
- ************** - 250 => For vm 

Install server:
- use the debian minimal, about 300MB. 
- hostname:     k8s-node-001, k8s-node-002, k8s-node-003 
- ip:           *************, *************, *************
- gateway:      ************
- dns server:   ************
- OS Disk:      use 20GB of HDD to install debian
- username:     datatp/Bee@Cloud
- net-tools:    apt-get update -y && apt-get install net-tools -y
- port forward: 32221 => *************:22, 32222 => *************:22, 32223 => *************:22, 

Check list:
- ping *******, ping google.com.vn
- ssh using  port forward
- Check disk device ssd 