# Guide to Installing & Mounting Directories with Rancher Desktop

## 1. Install Rancher Desktop

1.1. Visit the [Rancher Desktop Installation](https://docs.rancherdesktop.io/getting-started/installation) page  
1.2. Choose the version suitable for your system  
1.3. Install Rancher Desktop  

---

## 2. Settings (macOS)

2.1. Open **Preferences**  
2.2. Go to **Volumes** → select **virtiofs**  
2.3. Go to **Emulation** → select **VZ**  

---

## 3. Mount Local Directory

### 3.1. Check & Set Up rdctl

Check if the `rdctl` command is available, if not, set it up:
- **On macOS:**
    ```bash
    ~/.rd/bin/rdctl
    echo 'export PATH="$HOME/.rd/bin:$PATH"' >> ~/.zshrc
    source ~/.zshrc
    rdctl version
    ```
- **On Windows:**
    - `C:\Users\<USER>\AppData\Local\Programs\Rancher Desktop\resources\resources\linux\rdctl.exe`

### 3.2. Mount the Directory

Use the following commands:
```bash
rdctl shell -- mkdir -p /opt/rancher/mnt/cloud/datatp-local/servers
rdctl shell -- ls -l /opt/rancher/mnt/cloud/datatp-local
```

---

### 3.3. Configure Mounts

- Check the `rancher-desktop/lima/_config` directory on your machine
- Find the `override.yaml` file, or create it if it does not exist:
    ```bash
    touch override.yaml
    ```
- Paste the following into `override.yaml`:
    ```yaml
    mounts:
      - location: /opt/rancher
        writable: true
    ```
- Restart Rancher Desktop
