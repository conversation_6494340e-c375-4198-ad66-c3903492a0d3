<!doctype html>
<html lang="en-GB" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-shared/developer/CHANGELOG" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.5.2">
<title data-rh="true">Changelog | DataTP Cloud Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:url" content="https://docs.beelogistics.cloud/en/docs/shared/developer/CHANGELOG"><meta data-rh="true" property="og:locale" content="en_GB"><meta data-rh="true" property="og:locale:alternate" content="vi_VN"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Changelog | DataTP Cloud Documentation"><meta data-rh="true" name="description" content="All notable changes to this project will be documented in this file."><meta data-rh="true" property="og:description" content="All notable changes to this project will be documented in this file."><link data-rh="true" rel="icon" href="/en/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://docs.beelogistics.cloud/en/docs/shared/developer/CHANGELOG"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/shared/developer/CHANGELOG" hreflang="vi-VN"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/en/docs/shared/developer/CHANGELOG" hreflang="en-GB"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/shared/developer/CHANGELOG" hreflang="x-default"><link rel="stylesheet" href="/en/assets/css/styles.28471e28.css">
<script src="/en/assets/js/runtime~main.f35a8d23.js" defer="defer"></script>
<script src="/en/assets/js/main.a817859e.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Skip to main content"><a class="skipToContent_pkKc" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/en/"><div class="navbar__logo"><img src="/en/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--light_NBvQ"><img src="/en/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--dark_oqmI"></div></a><a class="navbar__item navbar__link" href="/en/docs/shared/user/system">User Guides<!-- --></a><a class="navbar__item navbar__link" href="/en/docs/shared/developer/SETUP">Developer<!-- --></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_e5gE"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>English<!-- --></a><ul class="dropdown__menu"><li><a href="/docs/shared/developer/CHANGELOG" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="vi-VN">Tiếng Việt<!-- --></a></li><li><a href="/en/docs/shared/developer/CHANGELOG" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="en-GB">English<!-- --></a></li></ul></div><div class="toggle_VLGw colorModeToggle_FIch"><button class="clean-btn toggleButton_ZTlQ toggleButtonDisabled_NvtF" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_GYGu"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_nQ2C"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_qR4N"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper__olq"><div class="docsWrapper_fuhk"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_wMuW" type="button"></button><div class="docRoot_h7H2"><aside class="theme-doc-sidebar-container docSidebarContainer_xh3h"><div class="sidebarViewport_Kajm"><div class="sidebar_BiNU"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_OOpE"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/en/docs/shared/developer/SETUP">Setup<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/en/docs/shared/developer/CHANGELOG">Changelog<!-- --></a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/en/docs/shared/developer/BACKLOG">Backlog<!-- --></a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/en/docs/datatp-crm/developer/BACKLOG">CRM</a></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret" role="button" aria-expanded="false" href="/en/docs/document-ie/developer/CHANGELOG">Document IE</a></div></li></ul></nav><button type="button" title="Collapse sidebar" aria-label="Collapse sidebar" class="button button--secondary button--outline collapseSidebarButton_Lokv"><svg width="20" height="20" aria-hidden="true" class="collapseSidebarButtonIcon_ic5_"><g fill="#7a7a7a"><path d="M9.992 10.023c0 .2-.062.399-.172.547l-4.996 7.492a.982.982 0 01-.828.454H1c-.55 0-1-.453-1-1 0-.2.059-.403.168-.551l4.629-6.942L.168 3.078A.939.939 0 010 2.528c0-.548.45-.997 1-.997h2.996c.352 0 .649.18.828.45L9.82 9.472c.11.148.172.347.172.55zm0 0"></path><path d="M19.98 10.023c0 .2-.058.399-.168.547l-4.996 7.492a.987.987 0 01-.828.454h-3c-.547 0-.996-.453-.996-1 0-.2.059-.403.168-.551l4.625-6.942-4.625-6.945a.939.939 0 01-.168-.55 1 1 0 01.996-.997h3c.348 0 .649.18.828.45l4.996 7.492c.11.148.168.347.168.55zm0 0"></path></g></svg></button></div></div></aside><main class="docMainContainer_hm5G"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col"><div class="docItemContainer_oFeY"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_IPd0" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/en/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_ttLL"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li></ul></nav><div class="theme-doc-markdown markdown"><header><h1>Changelog</h1></header>
<!-- --><p>All notable changes to this project will be documented in this file.</p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250621">[R20250621]<!-- --><a href="#r20250621" class="hash-link" aria-label="Direct link to [R20250621]" title="Direct link to [R20250621]">​</a></h3>
<!-- --><p>Cập nhật nhánh develop</p>
<!-- --><p>Cập nhật dự án datatp-crm:</p>
<!-- --><ul>
<!-- --><li>Checkout code (nhánh develop) và setup:  git@gitlab<!-- -->:datatp<!-- -->.net<!-- -->:tuan<!-- -->/datatp-crm.git<!-- --></li>
<!-- --><li>Remove node_modules pnpm-lock.yaml dist và pnpm install &amp;&amp; pnpm run build ở các dự án lib, erp, document-ie, logistics, crm, phoenix.</li>
<!-- --><li>Build java code bằng command: ./datatp.sh lgc<!-- -->:build<!-- --> -clean -build<!-- --></li>
<!-- --><li>Run code như bình thường.</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250621-1">[R20250621]<!-- --><a href="#r20250621-1" class="hash-link" aria-label="Direct link to [R20250621]" title="Direct link to [R20250621]">​</a></h3>
<!-- --><p>Cập nhật nhánh document-ie:</p>
<!-- --><ul>
<!-- --><li>Em clear bảng nằm trên database cũ và fix bug bên doument set ạ</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh tms:
[Quân] :</p>
<!-- --><ol>
<!-- --><li>Cho phép cus tự lên thông tin xe thay điều vận:</li>
<!-- --></ol>
<!-- --><ul>
<!-- --><li>Giao diện TMSBillList hiển thị popup các tab VehicleTrip Form cho phép nhập thông tin lô hàng</li>
<!-- --></ul>
<!-- --><ol start="2">
<!-- --><li>Tự động ghép chuyến cho các lô hàng theo request của cus:</li>
<!-- --></ol>
<!-- --><ul>
<!-- --><li>Giao diện: Thêm 1 checkbox Combine Trip tại popup Processing Goods Request</li>
<!-- --><li>Backend: Tạo VehicleTrip chung cho các VehicleTripGoodsTracking được tạo từ các TMSBill request.</li>
<!-- --></ul>
<!-- --><ol start="3">
<!-- --><li>Export template xuất thông tin xe cho BFSOne theo feedback của người dùng.
Thêm note cảnh báo các lô chưa đủ điều kiện thanh toán(Sai hbl, chưa nhập giá, sai đơn vị)</li>
<!-- --></ol>
<!-- --><p>Chiến:</p>
<!-- --><ul>
<!-- --><li>Dựng Entity TMSHouseBill: Số Hbl, Loại Hình(Nhập/Xuất), Customer, Kho bãi lấy/trả hàng, Carrier, COT/ETA,...</li>
<!-- --><li>Thêm các hàm logic sử lý CRUD. Tối ưu hàm search khi join bảng tms-bill</li>
<!-- --><li>Migration TMS HouseBill data, tạo liên kết đến TMSBill tương ứng</li>
<!-- --><li>Dựng màn hình cho TMSHouseBillList và TMSHouseBillEditor</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><ul>
<!-- --><li>migrate<!-- -->:run<!-- --> --script document/CleanDocument.groovy<!-- --></li>
<!-- --><li>server:migrate<!-- -->:run<!-- --> --script tms/MigrationTMSHouseBill.groovy<!-- --></li>
<!-- --></ul>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250620">[R20250620]<!-- --><a href="#r20250620" class="hash-link" aria-label="Direct link to [R20250620]" title="Direct link to [R20250620]">​</a></h3>
<!-- --><p>Cập nhật nhánh document-ie:
Em fix bug search document set ạ</p>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250618">[*********]<!-- --><a href="#r20250618" class="hash-link" aria-label="Direct link to [*********]" title="Direct link to [*********]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:
[Dan] - Implement UI Dashboard Salesman Activity Tracker.</p>
<!-- --><p>Cập nhật dự án document-ie:</p>
<!-- --><ul>
<!-- --><li>Checkout code (nhánh develop):  git@gitlab<!-- -->:datatp<!-- -->.cloud<!-- -->:tuan<!-- -->/datatp-document-ie.git<!-- --></li>
<!-- --><li>Tạo db, user và restore theo link db tương ứng ở dưới. (Sửa thông tin ở file env.sh, chạy lại các lệnh ở file postgres-admin.sh)
db_name: document_ie_db
username: document_ie
password: document_ie</li>
<!-- --></ul>
<!-- --><p>Xem lại cấu hình các file config application.properties, bổ sung thêm datasource cho db mới.
document-ie:
type: com.zaxxer.hikari.HikariDataSource
connectionTimeout: 30000
idleTimeout: 600000
maxLifetime: 600000
minimumIdle: 5
maximumPoolSize: 15
auto-commit: false
driverClassName: <!-- --><code>***********************************************:{spring.datasource.server.port}/document_ie_db</code>
username: document_ie
password: document_ie<!-- --></p>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
Có thể download db document_ie_db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/document_ie_db-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/document_ie_db-latest.dump</a></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250618-1">[*********]<!-- --><a href="#r20250618-1" class="hash-link" aria-label="Direct link to [*********]" title="Direct link to [*********]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>[Dan] - Implement UI Dashboard cho Pricing Company.</li>
<!-- --></ul>
<!-- --><p>[Nhat]</p>
<!-- --><ul>
<!-- --><li>Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail</li>
<!-- --><li>Check Customer theo Tax Code: Bổ sung Button Check, Check thêm Customer Lead</li>
<!-- --><li>Thêm button tạo Shipping Instruction từ Partner Obligation, search Partner Obligation theo Cus/ Docs</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250617">[R20250617]<!-- --><a href="#r20250617" class="hash-link" aria-label="Direct link to [R20250617]" title="Direct link to [R20250617]">​</a></h3>
<!-- --><p>Cập nhật nhánh quan-tms:</p>
<!-- --><ul>
<!-- --><li>Thêm nút clear trạng thái thanh toán với quyền moderator tại màn hình TMSBillList</li>
<!-- --><li>Điều chỉnh hiển thị các điểm dừng tại màn TMSBillList, VehicleTripGoodTrackingList theo yêu cầu</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250616">[R20250616]<!-- --><a href="#r20250616" class="hash-link" aria-label="Direct link to [R20250616]" title="Direct link to [R20250616]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>
<!-- --><p>[An] - Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[An] -Refactor lại thông tin BFSOne Partner, Viết cron sync tự động theo ngày</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[An] - &quot;Cập nhật bảng giá cont (Trucking): Chia lại mức giá cho cont 20&#x27; theo y/c của HPH.</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Nhat] - Clean code Uncompleted Sale Daily Task, thay thế logic sendMessage cũ bằng CRMMessageSystem</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Nhat] - Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Dan] - Implement UI Dashboard cho CRM Company.</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Dan] - Viết api gen token, api cập nhật BFSOne Partner Code cho a Quý.
Quy trình lúc tạo mới partner -&gt; KT approve -&gt; BFSOne gen code partner -&gt; Gọi api để sync lại với DataTP
Đã gửi lại api cho a Quý lúc  13/06
<!-- --><a href="https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing" target="_blank" rel="noopener noreferrer">https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing</a>&quot;<!-- --></p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Dan] - Tạo bảng dữ liệu, lưu thông tin khách hàng
Tạo các api service cho phép cập nhật, chỉnh sửa và xoá record.</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Dan] - &quot;Cập nhật api liên quan đến authorization, cho phép các hệ thống bên ngoài gọi vào các service để cập nhật dữ liệu.
Hard code token, gửi cho client sau đó kiểm tra request để xác thực.&quot;</p>
<!-- --></li>
<!-- --><li>
<!-- --><p>[Dan] - Tạo bảng dữ liệu, lưu thông tin unit
Tạo các api service cho phép sync, cập nhật, chỉnh sửa và xoá record.</p>
<!-- --></li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh quan-tms:</p>
<!-- --><ul>
<!-- --><li>Giao diện TMSBillList, VehicleTripGoodTrackingList cho phép nhập thêm các điểm dừng</li>
<!-- --><li>Hàm search TMSBillList, VehicleTripGoodTrackingList load thêm các điểm dừng</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250612">[R20250612]<!-- --><a href="#r20250612" class="hash-link" aria-label="Direct link to [R20250612]" title="Direct link to [R20250612]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>Refactor lại thông tin BFSOne Partner</li>
<!-- --><li>Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh tms:</p>
<!-- --><ul>
<!-- --><li>[Quân] Drop bảng document_document_set_category, drop column document_category_id tại bảng document_document_set,  drop các index đặt tên sai tại bảng document_document</li>
<!-- --><li>[Chiến] Fix bugs TMSBillFee save lỗi, Clear thông tin xe và giá cost khi copy lô hàng</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><ul>
<!-- --><li>migrate<!-- -->:run<!-- --> --script document/CleanDocument.groovy<!-- --></li>
<!-- --></ul>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250612-1">[R20250612]<!-- --><a href="#r20250612-1" class="hash-link" aria-label="Direct link to [R20250612]" title="Direct link to [R20250612]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>Refactor lại thông tin BFSOne Partner</li>
<!-- --><li>Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh tms:</p>
<!-- --><ul>
<!-- --><li>Địa chỉ config cho khách hàng thêm StreetName để lưu số nhà, đường,...(Kế toán yêu cầu tách)</li>
<!-- --><li>Thêm senderStreetName, receiverStreetName trên TMSBill và đồng bộ với địa chỉ khách hàng đã config</li>
<!-- --><li>Fix BFSOne template export, chuẩn hóa địa chỉ  xã/phường, quận/huyên theo yêu cầu</li>
<!-- --><li>Fix lỗi query liên quan đến migration TMSVendor sang Vehicle Fleet (Quân chưa thay hết)</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250612-2">[R20250612]<!-- --><a href="#r20250612-2" class="hash-link" aria-label="Direct link to [R20250612]" title="Direct link to [R20250612]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>Clean code groovy sql không sử dụng từ datatp-build/app/cli</li>
<!-- --><li>Drop các colume thừa, tạo nhầm, không còn sử dụng ở các bảng bfsone_partner, lgc_price_bulk_cargo_inquiry_request, lgc_price_truck_container_charge</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh nhat:</p>
<!-- --><ul>
<!-- --><li>Chỉnh sửa giao diện Asset Calendar+ fix bug màn hình tạo Task</li>
<!-- --><li>Bổ sung màn hình Admin KPI</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh tms:
Task:
Chiến:</p>
<!-- --><ul>
<!-- --><li>Push cước từ Vehicle Goods Tracking về phần Chi phí TMSBill</li>
<!-- --><li>Cho phép cus đồng bộ cước khi Vehicle Goods Tracking đã được nhập cước</li>
<!-- --><li>Thêm trạng thái thông báo thanh toán chi phí TMSBill và các lỗi dẫn đến chưa đc thanh toán</li>
<!-- --><li>UITMSBillList lọc các bill chưa thanh toán</li>
<!-- --><li>Verify HouseBill tmsBill với BFSOne, cảnh báo các lô hàng HouseBill chưa Verify</li>
<!-- --></ul>
<!-- --><p>Quân:</p>
<!-- --><ul>
<!-- --><li>[Vehicle Fleet] Thêm field emails và các field webhook config</li>
<!-- --><li>Viết groovy script merge tms vendor vào vehicle fleet và migration dữ liệu các entity dùng TMSVendor sang Vehicle Fleets</li>
<!-- --><li>Thay thế trên các giao diện màn hình dùng BBRefTMSVendor sang BBRefVehicleFleet.</li>
<!-- --><li>Chuyển và kiểm tra các chức năng call webhook được cấu hình từ TMS Partner sang Vehicle Fleet</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><ul>
<!-- --><li>migrate<!-- -->:run<!-- --> --script crm/AlterTables.groovy<!-- --></li>
<!-- --><li>migrate<!-- -->:run<!-- --> --script tms/MigrationTmsBillFee.groovy<!-- --></li>
<!-- --><li>server:migrate<!-- -->:run<!-- --> --script tms/MigrationVehicleFleet.groovy<!-- --></li>
<!-- --></ul>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250611">[R20250611]<!-- --><a href="#r20250611" class="hash-link" aria-label="Direct link to [R20250611]" title="Direct link to [R20250611]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>Sửa logo báo cáo</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh asset:
Task:</p>
<!-- --><ul>
<!-- --><li>[Asset] Thêm giao diện Calendar riêng cho book xe + phòng họp (trong module Asset). Default xem ở dạng week</li>
<!-- --><li>[Spreadsheet] Tạo bảng config màn hình Report team IST (BD HCM)</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh ocr:
Em fix bug trích rút bên kế toán ạ</p>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="r20250610">[R20250610]<!-- --><a href="#r20250610" class="hash-link" aria-label="Direct link to [R20250610]" title="Direct link to [R20250610]">​</a></h3>
<!-- --><p>Cập nhật nhánh crm:</p>
<!-- --><ul>
<!-- --><li>Cập nhật form, feedback chỉnh sửa, bugs cho inquiry hàng rời (Team BD)</li>
<!-- --><li>Enhance các báo cáo ở màn hình CRM. (Lọc, filter, xuất excel, ..)
Báo cáo hoạt động khách hàng/ lead theo dõi gần đây</li>
<!-- --><li>Fix bugs lỗi spam mail nhắc cập nhật request</li>
<!-- --><li>Cập nhật response MSA: String =&gt; MapObject</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh tms:</p>
<!-- --><ul>
<!-- --><li>Fix bugs liên quan đến GPS</li>
<!-- --><li>Fix bugs TMS liên quan đến TMSPartner và TMSBillFee</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><h1>Cập nhật server , code nhánh develop và download db</h1>
<!-- --><p>Cập nhật nhánh ocr:</p>
<!-- --><ul>
<!-- --><li>Bổ sung trích rút tên người bán người mua bên python và thêm vào bên giao diện</li>
<!-- --><li>cập nhật trích rút bên dat-ocr, code datatp-python</li>
<!-- --></ul>
<!-- --><p>Cập nhật nhánh maintenance</p>
<!-- --><ul>
<!-- --><li>Remove react-beautiful-dnd thư viện</li>
<!-- --><li>Cập nhật kanban sử dụng dndkit lib</li>
<!-- --></ul>
<!-- --><p>Có thể download db mới tại đường dẫn <!-- --><a href="https://beelogistics.cloud/download/datatpdb-latest.dump" target="_blank" rel="noopener noreferrer">https://beelogistics.cloud/download/datatpdb-latest.dump</a>
hoặc chạy run update và các migrate script<!-- --></p>
<!-- --><p>Nếu không cập nhật db, chạy script:</p>
<!-- --><ul>
<!-- --><li>migrate<!-- -->:run<!-- --> --script tms/MigrationTmsPartnerAddess.groovy<!-- --></li>
<!-- --></ul></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"></nav></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 DataTP Cloud.</div></div></div></footer></div>
</body>
</html>