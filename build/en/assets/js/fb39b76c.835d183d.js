"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[403],{4418:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>u,contentTitle:()=>c,default:()=>l,frontMatter:()=>s,metadata:()=>i,toc:()=>a});var r=n(216),o=n(9304);const s={},c=void 0,i={id:"document-ie/user/intro",title:"intro",description:"",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/document-ie/user/intro.md",sourceDirName:"document-ie/user",slug:"/document-ie/user/intro",permalink:"/en/docs/document-ie/user/intro",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{},sidebar:"userSidebar",previous:{title:"OKR - C\u1ea5p qu\u1ea3n l\xfd.",permalink:"/en/docs/shared/user/okr/okr_manager"},next:{title:"T\u1ed5ng quan",permalink:"/en/docs/datatp-crm/user/crm/overview"}},u={},a=[];function d(e){return(0,r.jsx)(r.Fragment,{})}function l(e={}){const{wrapper:t}={...(0,o.R)(),...e.components};return t?(0,r.jsx)(t,{...e,children:(0,r.jsx)(d,{...e})}):d()}},9304:(e,t,n)=>{n.d(t,{R:()=>c,x:()=>i});var r=n(6372);const o={},s=r.createContext(o);function c(e){const t=r.useContext(s);return r.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function i(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(o):e.components||o:c(e.components),r.createElement(s.Provider,{value:t},e.children)}}}]);