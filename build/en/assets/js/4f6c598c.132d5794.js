"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[525],{6386:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>a,contentTitle:()=>c,default:()=>h,frontMatter:()=>r,metadata:()=>l,toc:()=>o});var t=s(216),i=s(9304);const r={sidebar_position:3,toc:!1,hide_table_of_contents:!0},c="Inquiry Request",l={id:"crm/references/mail_request",title:"Inquiry Request",description:"Feature to send price check requests via email when no suitable prices are found in the Pricing Tools.",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/crm/references/mail_request.md",sourceDirName:"crm/references",slug:"/crm/references/mail_request",permalink:"/en/docs/crm/references/mail_request",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/crm/references/mail_request.md",tags:[],version:"current",sidebarPosition:3,frontMatter:{sidebar_position:3,toc:!1,hide_table_of_contents:!0},sidebar:"tutorialSidebar",previous:{title:"Price Search",permalink:"/en/docs/crm/references/search_prices"},next:{title:"Quotation & Internal Booking",permalink:"/en/docs/crm/references/quotation"}},a={},o=[{value:"Choose the appropriate request type",id:"choose-the-appropriate-request-type",level:3},{value:"Fill in request information",id:"fill-in-request-information",level:3},{value:"Email",id:"email",level:4},{value:"Attachments",id:"attachments",level:4},{value:"Managing Sent Requests",id:"managing-sent-requests",level:2},{value:"Available functions on the screen",id:"available-functions-on-the-screen",level:4},{value:"Main Functions",id:"main-functions",level:3}];function d(e){const n={a:"a",blockquote:"blockquote",code:"code",em:"em",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",img:"img",li:"li",ol:"ol",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.header,{children:(0,t.jsx)(n.h1,{id:"inquiry-request",children:"Inquiry Request"})}),"\n",(0,t.jsxs)(n.p,{children:["Feature to send price check requests via email when no suitable prices are found in the ",(0,t.jsx)(n.strong,{children:"Pricing Tools"}),"."]}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsx)(n.li,{children:"Search for prices for the route you need to quote"}),"\n",(0,t.jsxs)(n.li,{children:["Click ",(0,t.jsx)(n.code,{children:"Request Pricing"})," on the toolbar if no suitable results are found"]}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.img,{alt:"mail_request.png",src:s(4629).A+"",width:"1681",height:"967"})}),"\n",(0,t.jsx)(n.h3,{id:"choose-the-appropriate-request-type",children:"Choose the appropriate request type"}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.strong,{children:"FCL Import"}),"\n",(0,t.jsx)(n.img,{alt:"mail_request_type_1.png",src:s(666).A+"",width:"1918",height:"1075"})]}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.strong,{children:"FCL Export"}),"\n",(0,t.jsx)(n.img,{alt:"mail_request_type_2.png",src:s(1777).A+"",width:"1916",height:"1076"})]}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.strong,{children:"Trucking"}),"\n",(0,t.jsx)(n.img,{alt:"mail_request_type_3.png",src:s(1560).A+"",width:"1915",height:"1083"})]}),"\n",(0,t.jsx)(n.h3,{id:"fill-in-request-information",children:"Fill in request information"}),"\n",(0,t.jsx)(n.h4,{id:"email",children:"Email"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"From"}),": Sender's email"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"TO"}),": Pricing team email ",(0,t.jsx)(n.em,{children:"(auto-filled)"})]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"TO (External)"}),": External recipient's email"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"CC"}),": Internal CC email addresses"]}),"\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"CC (External)"}),": External CC email addresses"]}),"\n"]}),"\n",(0,t.jsx)(n.h4,{id:"attachments",children:"Attachments"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Drag and drop or select files (maximum 35MB)"}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.img,{alt:"mail_request_from.png",src:s(316).A+"",width:"1444",height:"846"})}),"\n",(0,t.jsxs)(n.blockquote,{children:["\n",(0,t.jsxs)(n.p,{children:["Click ",(0,t.jsx)(n.code,{children:"Send Request"})," when completed"]}),"\n"]}),"\n",(0,t.jsx)(n.h2,{id:"managing-sent-requests",children:"Managing Sent Requests"}),"\n",(0,t.jsxs)(n.p,{children:["View list of sent requests: ",(0,t.jsx)(n.strong,{children:"Transactions"})," > ",(0,t.jsx)(n.strong,{children:"Inquiry"})," tab ",(0,t.jsx)(n.strong,{children:"(1)"})]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.img,{alt:"mail_request_list.png",src:s(5614).A+"",width:"1921",height:"905"})}),"\n",(0,t.jsx)(n.h4,{id:"available-functions-on-the-screen",children:"Available functions on the screen"}),"\n",(0,t.jsx)(n.h3,{id:"main-functions",children:"Main Functions"}),"\n",(0,t.jsxs)(n.ol,{children:["\n",(0,t.jsxs)(n.li,{children:["\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.strong,{children:"Update Inquiry Request feedback and status"})}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Update customer feedback and request status (Win/ Received Response/ Price Not Match/ ...)."}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.img,{alt:"request_change_status.png",src:s(6475).A+"",width:"1916",height:"896"})}),"\n",(0,t.jsxs)(n.p,{children:[(0,t.jsx)(n.em,{children:"Video demo"}),": ",(0,t.jsx)(n.a,{href:"https://youtu.be/8DMXdL3haIc",children:"https://youtu.be/8DMXdL3haIc"})]}),"\n",(0,t.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,t.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/8DMXdL3haIc",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,t.jsx)("br",{}),"\n",(0,t.jsxs)(n.ol,{start:"2",children:["\n",(0,t.jsxs)(n.li,{children:[(0,t.jsx)(n.strong,{children:"Resend request"}),"\n",(0,t.jsxs)(n.ul,{children:["\n",(0,t.jsx)(n.li,{children:"Click the icon at the beginning of the row to copy information from the old request"}),"\n",(0,t.jsx)(n.li,{children:"Edit and send as a new request"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,t.jsx)(n.p,{children:(0,t.jsx)(n.img,{alt:"resend_mail.png",src:s(1725).A+"",width:"1920",height:"1078"})})]})}function h(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,t.jsx)(n,{...e,children:(0,t.jsx)(d,{...e})}):d(e)}},4629:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/mail_request-8eda5978f7a30a44d34bc45cd3240b52.png"},316:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/mail_request_form-9637169814928ec51381d6edc59a2cda.png"},5614:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/mail_request_list-9f9fc284ed086441d01caff933ec3459.png"},666:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/mail_request_type_1-0862e5dfcdcdc791b1bb2a1d75d94bc7.png"},1777:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/mail_request_type_2-a15d5fd49a277e34907dfaf92b084529.png"},1560:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/mail_request_type_3-c36b02086e42ab15ccd333214d38ee58.png"},6475:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/request_change_status-0dbc459b9588934cf5538e789ab1846e.png"},1725:(e,n,s)=>{s.d(n,{A:()=>t});const t=s.p+"assets/images/resend_mail-27c4f7811cf9a5dd355bf36e0614cbc9.png"},9304:(e,n,s)=>{s.d(n,{R:()=>c,x:()=>l});var t=s(6372);const i={},r=t.createContext(i);function c(e){const n=t.useContext(r);return t.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function l(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:c(e.components),t.createElement(r.Provider,{value:n},e.children)}}}]);