"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[714],{105:(e,n,s)=>{s.r(n),s.d(n,{assets:()=>c,contentTitle:()=>o,default:()=>h,frontMatter:()=>i,metadata:()=>a,toc:()=>d});var r=s(216),t=s(9304);const i={sidebar_position:1,hide_table_of_contents:!0},o="Overview",a={id:"crm/overview",title:"Overview",description:"Access the business module",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/crm/overview.md",sourceDirName:"crm",slug:"/crm/overview",permalink:"/en/docs/crm/overview",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/crm/overview.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0},sidebar:"tutorialSidebar",previous:{title:"CRM",permalink:"/en/docs/category/crm"},next:{title:"Partners",permalink:"/en/docs/crm/partners"}},c={},d=[{value:"1. <strong>Search Prices</strong>",id:"1-search-prices",level:4},{value:"2. <strong>Create and send inquiry requests</strong>",id:"2-create-and-send-inquiry-requests",level:4},{value:"3. <strong>Create, edit quotations, send Internal Booking</strong>",id:"3-create-edit-quotations-send-internal-booking",level:4},{value:"4. Create and manage potential customer information (Leads)",id:"4-create-and-manage-potential-customer-information-leads",level:4},{value:"5. Create and manage customer information",id:"5-create-and-manage-customer-information",level:4}];function l(e){const n={a:"a",code:"code",em:"em",h1:"h1",h4:"h4",header:"header",img:"img",li:"li",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,t.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"overview",children:"Overview"})}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.strong,{children:"Access the business module"})}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["On the left corner of the screen, click on the company logo -> select ",(0,r.jsx)(n.code,{children:"CRM"}),"."]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.img,{alt:"../img/sales/sale_access.gif",src:s(1009).A+"",width:"1920",height:"1080"})}),"\n",(0,r.jsxs)(n.h4,{id:"1-search-prices",children:["1. ",(0,r.jsx)(n.strong,{children:"Search Prices"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Allows users to search for prices available in the system."}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.a,{href:"./references/search_prices",children:"See detailed guide here"})}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.h4,{id:"2-create-and-send-inquiry-requests",children:["2. ",(0,r.jsx)(n.strong,{children:"Create and send inquiry requests"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:["Allows users to send price check requests via email when suitable prices are not found in ",(0,r.jsx)(n.strong,{children:"Pricing Tools"}),"."]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Review sent requests, update customer feedback, and request status."}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.a,{href:"./references/mail_request",children:"See detailed guide here"})}),"\n",(0,r.jsxs)(n.h4,{id:"3-create-edit-quotations-send-internal-booking",children:["3. ",(0,r.jsx)(n.strong,{children:"Create, edit quotations, send Internal Booking"})]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsx)(n.p,{children:"Allows users to create quotations, export quotations to Excel files, or send quotation emails to customers."}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:["Create IB, send information via ",(0,r.jsx)(n.code,{children:"BFSOne"})," to customers to proceed with job file creation"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.a,{href:"./references/quotation",children:"See detailed guide here"})}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.em,{children:"Quotation Management Policy"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"- Each quotation has only one owner\n- Owners have full rights to view and manage their quotations\n- Other users cannot view/edit quotations that are not under their management\n- This permission system ensures information security\n"})}),"\n",(0,r.jsx)(n.h4,{id:"4-create-and-manage-potential-customer-information-leads",children:"4. Create and manage potential customer information (Leads)"}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.a,{href:"./references/lead_management",children:"See detailed guide here"})}),"\n",(0,r.jsx)(n.h4,{id:"5-create-and-manage-customer-information",children:"5. Create and manage customer information"}),"\n",(0,r.jsx)(n.p,{children:(0,r.jsx)(n.a,{href:"./references/customer_management",children:"See detailed guide here"})}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"Customer information is automatically synchronized from the BFSOne system.\nIf there are any errors or updates needed, please contact IT for support.\n"})})]})}function h(e={}){const{wrapper:n}={...(0,t.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(l,{...e})}):l(e)}},1009:(e,n,s)=>{s.d(n,{A:()=>r});const r=s.p+"assets/images/crm_access-6c642bd802f8f1235c540efcc62f2d7d.gif"},9304:(e,n,s)=>{s.d(n,{R:()=>o,x:()=>a});var r=s(6372);const t={},i=r.createContext(t);function o(e){const n=r.useContext(i);return r.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(t):e.components||t:o(e.components),r.createElement(i.Provider,{value:n},e.children)}}}]);