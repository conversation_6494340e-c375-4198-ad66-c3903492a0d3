"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[366],{9249:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>c,contentTitle:()=>r,default:()=>u,frontMatter:()=>s,metadata:()=>a,toc:()=>d});var o=t(216),i=t(9304);const s={sidebar_position:4,hide_table_of_contents:!0},r="Quotation & Internal Booking",a={id:"crm/references/quotation",title:"Quotation & Internal Booking",description:"1. Create and Edit Quotations",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/crm/references/quotation.md",sourceDirName:"crm/references",slug:"/crm/references/quotation",permalink:"/en/docs/crm/references/quotation",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/crm/references/quotation.md",tags:[],version:"current",sidebarPosition:4,frontMatter:{sidebar_position:4,hide_table_of_contents:!0},sidebar:"tutorialSidebar",previous:{title:"Inquiry Request",permalink:"/en/docs/crm/references/mail_request"},next:{title:"Qu\u1ea3n l\xfd Lead",permalink:"/en/docs/crm/references/lead_management"}},c={},d=[{value:"1. <strong>Create and Edit Quotations</strong>",id:"1-create-and-edit-quotations",level:4},{value:"2. <strong>Managing Created Quotations/Internal Bookings</strong>",id:"2-managing-created-quotationsinternal-bookings",level:4}];function l(e){const n={a:"a",code:"code",em:"em",h1:"h1",h4:"h4",header:"header",img:"img",li:"li",p:"p",strong:"strong",ul:"ul",...(0,i.R)(),...e.components};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.header,{children:(0,o.jsx)(n.h1,{id:"quotation--internal-booking",children:"Quotation & Internal Booking"})}),"\n",(0,o.jsxs)(n.h4,{id:"1-create-and-edit-quotations",children:["1. ",(0,o.jsx)(n.strong,{children:"Create and Edit Quotations"})]}),"\n",(0,o.jsxs)(n.ul,{children:["\n",(0,o.jsxs)(n.li,{children:["In the ",(0,o.jsx)(n.code,{children:"CRM"})," module, search for prices as previously instructed or see link"]}),"\n"]}),"\n",(0,o.jsx)(n.p,{children:(0,o.jsxs)(n.em,{children:["Case Study: ",(0,o.jsx)(n.code,{children:"Quotation for FCL 2x40DC, NANSHA, CHINA -> HOCHIMINH, VIETNAM, FOB terms"})]})}),"\n",(0,o.jsxs)(n.p,{children:[(0,o.jsx)(n.em,{children:"Video demo"}),": ",(0,o.jsx)(n.a,{href:"https://youtu.be/6cwWaDSQWGg",children:"https://youtu.be/6cwWaDSQWGg"})]}),"\n",(0,o.jsx)("div",{style:{position:"relative",paddingBottom:"56.25%",height:0,overflow:"hidden",maxWidth:"100%",height:"auto"},children:(0,o.jsx)("iframe",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%"},src:"https://www.youtube.com/embed/6cwWaDSQWGg",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),"\n",(0,o.jsx)("br",{}),"\n",(0,o.jsx)(n.p,{children:(0,o.jsx)(n.strong,{children:"If system prices are not available"})}),"\n",(0,o.jsxs)(n.ul,{children:["\n",(0,o.jsxs)(n.li,{children:["Click ",(0,o.jsx)(n.code,{children:"Request a Quote"})," to create a custom quotation. Other operations are similar to the tutorial video above"]}),"\n"]}),"\n",(0,o.jsx)(n.p,{children:(0,o.jsx)(n.img,{alt:"./img/quotation_fcl_1.png",src:t(4260).A+"",width:"1925",height:"538"})}),"\n",(0,o.jsx)("hr",{}),"\n",(0,o.jsx)(n.p,{children:(0,o.jsx)(n.strong,{children:"Quotation for Multiple Lines"})}),"\n",(0,o.jsx)(n.p,{children:(0,o.jsx)(n.img,{alt:"./img/quotation_3.png",src:t(5872).A+"",width:"1922",height:"615"})}),"\n",(0,o.jsx)("hr",{}),"\n",(0,o.jsx)("hr",{}),"\n",(0,o.jsx)("br",{}),"\n",(0,o.jsxs)(n.h4,{id:"2-managing-created-quotationsinternal-bookings",children:["2. ",(0,o.jsx)(n.strong,{children:"Managing Created Quotations/Internal Bookings"})]}),"\n",(0,o.jsxs)(n.p,{children:["View list of created quotations: ",(0,o.jsx)(n.strong,{children:(0,o.jsx)(n.code,{children:"Transactions"})})," > ",(0,o.jsx)(n.strong,{children:(0,o.jsx)(n.code,{children:"Quotations"})})," tab ",(0,o.jsx)(n.strong,{children:"(2)"})]}),"\n",(0,o.jsx)(n.p,{children:(0,o.jsx)(n.img,{alt:"./img/quotation_fcl_1.png",src:t(73).A+"",width:"2555",height:"508"})}),"\n",(0,o.jsxs)(n.ul,{children:["\n",(0,o.jsxs)(n.li,{children:["Similarly for internal bookings: ",(0,o.jsx)(n.strong,{children:(0,o.jsx)(n.code,{children:"Transactions"})})," > ",(0,o.jsx)(n.strong,{children:(0,o.jsx)(n.code,{children:"Bookings"})})," tab ",(0,o.jsx)(n.strong,{children:"(3)"})]}),"\n"]})]})}function u(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,o.jsx)(n,{...e,children:(0,o.jsx)(l,{...e})}):l(e)}},73:(e,n,t)=>{t.d(n,{A:()=>o});const o=t.p+"assets/images/quotation_2-ee0c70b8cf2f395001b9a92fa9f25595.png"},5872:(e,n,t)=>{t.d(n,{A:()=>o});const o=t.p+"assets/images/quotation_3-02845660375e9e321ec8a3367f79c461.png"},4260:(e,n,t)=>{t.d(n,{A:()=>o});const o=t.p+"assets/images/quotation_fcl_1-18a771072ddbbba527ed37b9dcc519ca.png"},9304:(e,n,t)=>{t.d(n,{R:()=>r,x:()=>a});var o=t(6372);const i={},s=o.createContext(i);function r(e){const n=o.useContext(s);return o.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:r(e.components),o.createElement(s.Provider,{value:n},e.children)}}}]);