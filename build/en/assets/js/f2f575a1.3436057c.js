"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[922],{8188:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>a,contentTitle:()=>o,default:()=>l,frontMatter:()=>s,metadata:()=>c,toc:()=>p});var i=t(216),r=t(9304);const s={sidebar_position:1},o="KPI - Manager",c={id:"kpi/company_kpi",title:"KPI - Manager",description:"I. Truy c\u1eadp KPI",source:"@site/i18n/en/docusaurus-plugin-content-docs/current/kpi/company_kpi.md",sourceDirName:"kpi",slug:"/kpi/company_kpi",permalink:"/en/docs/kpi/company_kpi",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/kpi/company_kpi.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1},sidebar:"tutorialSidebar",previous:{title:"KPI",permalink:"/en/docs/category/kpi"},next:{title:"KPI - User",permalink:"/en/docs/kpi/user_kpi"}},a={},p=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function d(e){const n={h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",strong:"strong",...(0,r.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"kpi---manager",children:"KPI - Manager"})}),"\n",(0,i.jsx)(n.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:["Ch\u1ecdn ",(0,i.jsx)(n.strong,{children:"Menu"})]}),"\n",(0,i.jsxs)(n.li,{children:["Ch\u1ecdn App ",(0,i.jsx)(n.strong,{children:"KPIs"})]}),"\n"]})]})}function l(e={}){const{wrapper:n}={...(0,r.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(d,{...e})}):d(e)}},9304:(e,n,t)=>{t.d(n,{R:()=>o,x:()=>c});var i=t(6372);const r={},s=i.createContext(r);function o(e){const n=i.useContext(s);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:o(e.components),i.createElement(s.Provider,{value:n},e.children)}}}]);