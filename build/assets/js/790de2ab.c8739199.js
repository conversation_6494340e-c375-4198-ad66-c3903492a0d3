"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[811],{9212:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>i,contentTitle:()=>c,default:()=>l,frontMatter:()=>o,metadata:()=>a,toc:()=>u});var s=n(216),r=n(9304);const o={},c="CRM Features",a={id:"datatp-tms/developer/features",title:"CRM Features",description:"- Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng.",source:"@site/i18n/vi/docusaurus-plugin-content-docs/current/datatp-tms/developer/features.md",sourceDirName:"datatp-tms/developer",slug:"/datatp-tms/developer/features",permalink:"/docs/datatp-tms/developer/features",draft:!1,unlisted:!1,tags:[],version:"current",frontMatter:{}},i={},u=[];function d(e){const t={h1:"h1",header:"header",li:"li",p:"p",ul:"ul",...(0,r.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"crm-features",children:"CRM Features"})}),"\n",(0,s.jsxs)(t.ul,{children:["\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"Lead Management: Qu\u1ea3n l\xfd kh\xe1ch h\xe0ng ti\u1ec1m n\u0103ng."}),"\n"]}),"\n",(0,s.jsxs)(t.li,{children:["\n",(0,s.jsx)(t.p,{children:"Quotation Tracking: Theo d\xf5i b\xe1o gi\xe1 (t\xedch h\u1ee3p t\u1eeb CRM, tham kh\u1ea3o y\xeau c\u1ea7u ng\xe0y 15/05/2025)."}),"\n"]}),"\n"]})]})}function l(e={}){const{wrapper:t}={...(0,r.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(d,{...e})}):d(e)}},9304:(e,t,n)=>{n.d(t,{R:()=>c,x:()=>a});var s=n(6372);const r={},o=s.createContext(r);function c(e){const t=s.useContext(o);return s.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function a(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:c(e.components),s.createElement(o.Provider,{value:t},e.children)}}}]);