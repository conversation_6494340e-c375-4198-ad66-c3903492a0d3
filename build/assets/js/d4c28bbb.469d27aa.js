"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[126],{2551:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>s,contentTitle:()=>o,default:()=>h,frontMatter:()=>l,metadata:()=>a,toc:()=>r});var i=t(216),d=t(9304);const l={sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},o="Setup DataTP Project",a={id:"shared/developer/SETUP",title:"Setup DataTP Project",description:"Tools And Config Requirement",source:"@site/i18n/vi/docusaurus-plugin-content-docs/current/shared/developer/SETUP.md",sourceDirName:"shared/developer",slug:"/shared/developer/SETUP",permalink:"/docs/shared/developer/SETUP",draft:!1,unlisted:!1,tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0,displayed_sidebar:"developerSidebar"},sidebar:"developerSidebar"},s={},r=[{value:"Tools And Config Requirement",id:"tools-and-config-requirement",level:3},{value:"Other tools",id:"other-tools",level:5},{value:"Config",id:"config",level:4},{value:"Th\xeam SSH key v\xe0o t\xe0i kho\u1ea3n GitLab",id:"th\xeam-ssh-key-v\xe0o-t\xe0i-kho\u1ea3n-gitlab",level:4},{value:"Installation Projects",id:"installation-projects",level:3},{value:"T\u1ea1o th\u01b0 m\u1ee5c root v\xe0 clone d\u1ef1 \xe1n",id:"t\u1ea1o-th\u01b0-m\u1ee5c-root-v\xe0-clone-d\u1ef1-\xe1n",level:4},{value:"Backend",id:"backend",level:4},{value:"FrontEnd",id:"frontend",level:4},{value:"Database and enviroment",id:"database-and-enviroment",level:4},{value:"Start the development server",id:"start-the-development-server",level:4}];function c(e){const n={code:"code",h1:"h1",h3:"h3",h4:"h4",h5:"h5",header:"header",li:"li",ol:"ol",p:"p",pre:"pre",ul:"ul",...(0,d.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"setup-datatp-project",children:"Setup DataTP Project"})}),"\n",(0,i.jsx)(n.h3,{id:"tools-and-config-requirement",children:"Tools And Config Requirement"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Node.js (version > 23)"}),"\n",(0,i.jsx)(n.li,{children:"pnpm"}),"\n",(0,i.jsx)(n.li,{children:"Git"}),"\n",(0,i.jsx)(n.li,{children:"Java (version 21)"}),"\n",(0,i.jsx)(n.li,{children:"Gradle (version > 8.7)"}),"\n",(0,i.jsx)(n.li,{children:"VS Code, Eclipse, ..."}),"\n",(0,i.jsx)(n.li,{children:"Postgres Server (version 16 above)"}),"\n",(0,i.jsx)(n.li,{children:"DBeaver - Database Tool to explore a database and table relations"}),"\n"]}),"\n",(0,i.jsx)(n.h5,{id:"other-tools",children:"Other tools"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Python (version 3.10 above)"}),"\n",(0,i.jsx)(n.li,{children:"Docker"}),"\n",(0,i.jsx)(n.li,{children:"K3s"}),"\n"]}),"\n",(0,i.jsx)(n.h4,{id:"config",children:"Config"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsx)(n.li,{children:"Git"}),"\n"]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:'  git config --global user.email "<EMAIL>"\n  git config --global user.name "Your Name"\n\n  git config --global core.filemode false\n  #Line ending with unix style\n  git config --global core.autocrlf false\n'})}),"\n",(0,i.jsxs)(n.ol,{start:"2",children:["\n",(0,i.jsx)(n.li,{children:"SSH Key"}),"\n"]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:'# T\u1ea1o SSH key m\u1edbi\n\nssh-keygen -t ed25519 -b 4096 -C "<EMAIL>"\n\n# Kh\u1edfi \u0111\u1ed9ng SSH agent\neval "$(ssh-agent -s)"\n\n# Th\xeam SSH key v\xe0o SSH agent\nssh-add ~/.ssh/id_ed25519\n'})}),"\n",(0,i.jsx)(n.h4,{id:"th\xeam-ssh-key-v\xe0o-t\xe0i-kho\u1ea3n-gitlab",children:"Th\xeam SSH key v\xe0o t\xe0i kho\u1ea3n GitLab"}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"Sao ch\xe9p n\u1ed9i dung SSH key"}),"\n"]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:"cat ~/.ssh/id_ed25519.pub | pbcopy\n"})}),"\n",(0,i.jsxs)(n.ul,{children:["\n",(0,i.jsx)(n.li,{children:"\u0110\u0103ng nh\u1eadp v\xe0o GitLab"}),"\n",(0,i.jsx)(n.li,{children:"V\xe0o Settings > SSH Keys"}),"\n",(0,i.jsx)(n.li,{children:"D\xe1n SSH key v\xe0 \u0111\u1eb7t t\xean cho key"}),"\n"]}),"\n",(0,i.jsx)(n.h3,{id:"installation-projects",children:"Installation Projects"}),"\n",(0,i.jsx)(n.h4,{id:"t\u1ea1o-th\u01b0-m\u1ee5c-root-v\xe0-clone-d\u1ef1-\xe1n",children:"T\u1ea1o th\u01b0 m\u1ee5c root v\xe0 clone d\u1ef1 \xe1n"}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{className:"language-bash",children:"# T\u1ea1o th\u01b0 m\u1ee5c root\nmkdir datatp\ncd datatp\n\n# Clone c\xe1c d\u1ef1 \xe1n\ngit clone git@gitlab:datatp.net:tuan/datatp-core.git\ngit clone git@gitlab:datatp.net:tuan/datatp-erp.git\ngit clone git@gitlab:datatp.net:tuan/datatp-document-ie.git\ngit clone git@gitlab:datatp.net:tuan/datatp-logistics.git\ngit clone git@gitlab:datatp.net:tuan/datatp-crm.git\ngit clone git@gitlab:datatp.net:tuan/datatp-build.git\n"})}),"\n",(0,i.jsx)(n.h4,{id:"backend",children:"Backend"}),"\n",(0,i.jsxs)(n.p,{children:["C\xe0i \u0111\u1eb7t dependencies v\xe0 build cho t\u1eebng projects theo th\u1ee9 t\u1ef1 ",(0,i.jsx)(n.code,{children:"datatp-core"}),",",(0,i.jsx)(n.code,{children:"datatp-erp"}),", ",(0,i.jsx)(n.code,{children:"datatp-document-ie"}),", ",(0,i.jsx)(n.code,{children:"datatp-logistics"}),",\n",(0,i.jsx)(n.code,{children:"datatp-crm"}),", ",(0,i.jsx)(n.code,{children:"datatp-build"}),"."]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"cd datatp-core\ngradle clean build -x test\ngradle publishToMaven\n"})}),"\n",(0,i.jsx)(n.h4,{id:"frontend",children:"FrontEnd"}),"\n",(0,i.jsxs)(n.p,{children:["Install v\xe0 build webui cho c\xe1c d\u1ef1 \xe1n ",(0,i.jsx)(n.code,{children:"datatp-erp"}),", ",(0,i.jsx)(n.code,{children:"datatp-document-ie"}),", ",(0,i.jsx)(n.code,{children:"datatp-logistics"}),",\n",(0,i.jsx)(n.code,{children:"datatp-crm"}),", ",(0,i.jsx)(n.code,{children:"datatp-build"}),"."]}),"\n",(0,i.jsx)(n.pre,{children:(0,i.jsx)(n.code,{children:"cd datatp-erp/webui/lib\npnpm install\npnpm run build\n"})}),"\n",(0,i.jsx)(n.h4,{id:"database-and-enviroment",children:"Database and enviroment"}),"\n",(0,i.jsx)(n.h4,{id:"start-the-development-server",children:"Start the development server"}),"\n",(0,i.jsxs)(n.p,{children:["Open ",(0,i.jsx)(n.code,{children:"http://localhost:3000"})," to view the site."]})]})}function h(e={}){const{wrapper:n}={...(0,d.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(c,{...e})}):c(e)}},9304:(e,n,t)=>{t.d(n,{R:()=>o,x:()=>a});var i=t(6372);const d={},l=i.createContext(d);function o(e){const n=i.useContext(l);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(d):e.components||d:o(e.components),i.createElement(l.Provider,{value:n},e.children)}}}]);