"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[740],{9826:(t,e,n)=>{n.r(e),n.d(e,{assets:()=>a,contentTitle:()=>i,default:()=>l,frontMatter:()=>r,metadata:()=>c,toc:()=>d});var s=n(216),o=n(9304);const r={sidebar_position:1},i="TODO",c={id:"tms/TODO",title:"TODO",description:"",source:"@site/docs/tms/TODO.md",sourceDirName:"tms",slug:"/tms/TODO",permalink:"/zh-Hans/docs/tms/TODO",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/tms/TODO.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1},sidebar:"tutorialSidebar",previous:{title:"TMS",permalink:"/zh-Hans/docs/category/tms"},next:{title:"Project - \u0110\u1ea7u vi\u1ec7c",permalink:"/zh-Hans/docs/category/project---\u0111\u1ea7u-vi\u1ec7c"}},a={},d=[];function u(t){const e={h1:"h1",header:"header",...(0,o.R)(),...t.components};return(0,s.jsx)(e.header,{children:(0,s.jsx)(e.h1,{id:"todo",children:"TODO"})})}function l(t={}){const{wrapper:e}={...(0,o.R)(),...t.components};return e?(0,s.jsx)(e,{...t,children:(0,s.jsx)(u,{...t})}):u(t)}},9304:(t,e,n)=>{n.d(e,{R:()=>i,x:()=>c});var s=n(6372);const o={},r=s.createContext(o);function i(t){const e=s.useContext(r);return s.useMemo((function(){return"function"==typeof t?t(e):{...e,...t}}),[e,t])}function c(t){let e;return e=t.disableParentContext?"function"==typeof t.components?t.components(o):t.components||o:i(t.components),s.createElement(r.Provider,{value:e},t.children)}}}]);