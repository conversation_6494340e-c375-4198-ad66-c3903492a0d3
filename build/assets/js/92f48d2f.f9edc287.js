"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[567],{3690:(e,t,n)=>{n.r(t),n.d(t,{assets:()=>d,contentTitle:()=>o,default:()=>a,frontMatter:()=>r,metadata:()=>c,toc:()=>p});var s=n(216),i=n(9304);const r={sidebar_position:1,hide_table_of_contents:!0},o="KPI - User",c={id:"kpi/user_kpi",title:"KPI - User",description:"I. Truy c\u1eadp KPI",source:"@site/docs/kpi/user_kpi.md",sourceDirName:"kpi",slug:"/kpi/user_kpi",permalink:"/docs/kpi/user_kpi",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/kpi/user_kpi.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0},sidebar:"tutorialSidebar",previous:{title:"KPI - Manager",permalink:"/docs/kpi/company_kpi"},next:{title:"TMS",permalink:"/docs/category/tms"}},d={},p=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function l(e){const t={h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",strong:"strong",...(0,i.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(t.header,{children:(0,s.jsx)(t.h1,{id:"kpi---user",children:"KPI - User"})}),"\n",(0,s.jsx)(t.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,s.jsxs)(t.ol,{children:["\n",(0,s.jsxs)(t.li,{children:["Ch\u1ecdn ",(0,s.jsx)(t.strong,{children:"Menu"})]}),"\n",(0,s.jsxs)(t.li,{children:["Ch\u1ecdn App ",(0,s.jsx)(t.strong,{children:"My KPIs"})]}),"\n"]})]})}function a(e={}){const{wrapper:t}={...(0,i.R)(),...e.components};return t?(0,s.jsx)(t,{...e,children:(0,s.jsx)(l,{...e})}):l(e)}},9304:(e,t,n)=>{n.d(t,{R:()=>o,x:()=>c});var s=n(6372);const i={},r=s.createContext(i);function o(e){const t=s.useContext(r);return s.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function c(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:o(e.components),s.createElement(r.Provider,{value:t},e.children)}}}]);