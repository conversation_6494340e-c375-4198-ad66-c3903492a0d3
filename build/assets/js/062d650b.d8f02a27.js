"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[174],{5289:(e,t,o)=>{o.r(t),o.d(t,{assets:()=>a,contentTitle:()=>s,default:()=>p,frontMatter:()=>r,metadata:()=>i,toc:()=>d});var n=o(216),c=o(9304);const r={sidebar_position:1},s="Prject - \u0110\u1ea7u vi\u1ec7c",i={id:"project/TODO",title:"Prject - \u0110\u1ea7u vi\u1ec7c",description:"",source:"@site/docs/project/TODO.md",sourceDirName:"project",slug:"/project/TODO",permalink:"/zh-Hans/docs/project/TODO",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/project/TODO.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1},sidebar:"tutorialSidebar",previous:{title:"Project - \u0110\u1ea7u vi\u1ec7c",permalink:"/zh-Hans/docs/category/project---\u0111\u1ea7u-vi\u1ec7c"}},a={},d=[];function u(e){const t={h1:"h1",header:"header",...(0,c.R)(),...e.components};return(0,n.jsx)(t.header,{children:(0,n.jsx)(t.h1,{id:"prject---\u0111\u1ea7u-vi\u1ec7c",children:"Prject - \u0110\u1ea7u vi\u1ec7c"})})}function p(e={}){const{wrapper:t}={...(0,c.R)(),...e.components};return t?(0,n.jsx)(t,{...e,children:(0,n.jsx)(u,{...e})}):u(e)}},9304:(e,t,o)=>{o.d(t,{R:()=>s,x:()=>i});var n=o(6372);const c={},r=n.createContext(c);function s(e){const t=n.useContext(r);return n.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function i(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(c):e.components||c:s(e.components),n.createElement(r.Provider,{value:t},e.children)}}}]);