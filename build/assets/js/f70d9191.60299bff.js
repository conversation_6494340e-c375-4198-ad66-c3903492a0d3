"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[198],{4080:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>a,contentTitle:()=>r,default:()=>l,frontMatter:()=>o,metadata:()=>c,toc:()=>p});var i=t(216),s=t(9304);const o={sidebar_position:1,hide_table_of_contents:!0},r="KPI - Manager",c={id:"kpi/company_kpi",title:"KPI - Manager",description:"I. Truy c\u1eadp KPI",source:"@site/docs/kpi/company_kpi.md",sourceDirName:"kpi",slug:"/kpi/company_kpi",permalink:"/docs/kpi/company_kpi",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/kpi/company_kpi.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0},sidebar:"tutorialSidebar",previous:{title:"KPI",permalink:"/docs/category/kpi"},next:{title:"KPI - User",permalink:"/docs/kpi/user_kpi"}},a={},p=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function d(e){const n={h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",strong:"strong",...(0,s.R)(),...e.components};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(n.header,{children:(0,i.jsx)(n.h1,{id:"kpi---manager",children:"KPI - Manager"})}),"\n",(0,i.jsx)(n.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,i.jsxs)(n.ol,{children:["\n",(0,i.jsxs)(n.li,{children:["Ch\u1ecdn ",(0,i.jsx)(n.strong,{children:"Menu"})]}),"\n",(0,i.jsxs)(n.li,{children:["Ch\u1ecdn App ",(0,i.jsx)(n.strong,{children:"KPIs"})]}),"\n"]})]})}function l(e={}){const{wrapper:n}={...(0,s.R)(),...e.components};return n?(0,i.jsx)(n,{...e,children:(0,i.jsx)(d,{...e})}):d(e)}},9304:(e,n,t)=>{t.d(n,{R:()=>r,x:()=>c});var i=t(6372);const s={},o=i.createContext(s);function r(e){const n=i.useContext(o);return i.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(s):e.components||s:r(e.components),i.createElement(o.Provider,{value:n},e.children)}}}]);