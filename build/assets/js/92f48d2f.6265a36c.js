"use strict";(self.webpackChunkbeelogistics_docs=self.webpackChunkbeelogistics_docs||[]).push([[948],{3690:(e,n,t)=>{t.r(n),t.d(n,{assets:()=>d,contentTitle:()=>o,default:()=>l,frontMatter:()=>r,metadata:()=>c,toc:()=>a});var s=t(216),i=t(9304);const r={sidebar_position:1,hide_table_of_contents:!0},o="KPI - User",c={id:"kpi/user_kpi",title:"KPI - User",description:"I. Truy c\u1eadp KPI",source:"@site/docs/kpi/user_kpi.md",sourceDirName:"kpi",slug:"/kpi/user_kpi",permalink:"/zh-Hans/docs/kpi/user_kpi",draft:!1,unlisted:!1,editUrl:"https://github.com/ngcdan/beelogistics-docs/tree/main/docs/kpi/user_kpi.md",tags:[],version:"current",sidebarPosition:1,frontMatter:{sidebar_position:1,hide_table_of_contents:!0},sidebar:"tutorialSidebar",previous:{title:"KPI - Manager",permalink:"/zh-Hans/docs/kpi/company_kpi"},next:{title:"TMS",permalink:"/zh-Hans/docs/category/tms"}},d={},a=[{value:"I. Truy c\u1eadp KPI",id:"i-truy-c\u1eadp-kpi",level:2}];function p(e){const n={h1:"h1",h2:"h2",header:"header",li:"li",ol:"ol",strong:"strong",...(0,i.R)(),...e.components};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.header,{children:(0,s.jsx)(n.h1,{id:"kpi---user",children:"KPI - User"})}),"\n",(0,s.jsx)(n.h2,{id:"i-truy-c\u1eadp-kpi",children:"I. Truy c\u1eadp KPI"}),"\n",(0,s.jsxs)(n.ol,{children:["\n",(0,s.jsxs)(n.li,{children:["Ch\u1ecdn ",(0,s.jsx)(n.strong,{children:"Menu"})]}),"\n",(0,s.jsxs)(n.li,{children:["Ch\u1ecdn App ",(0,s.jsx)(n.strong,{children:"My KPIs"})]}),"\n"]})]})}function l(e={}){const{wrapper:n}={...(0,i.R)(),...e.components};return n?(0,s.jsx)(n,{...e,children:(0,s.jsx)(p,{...e})}):p(e)}},9304:(e,n,t)=>{t.d(n,{R:()=>o,x:()=>c});var s=t(6372);const i={},r=s.createContext(i);function o(e){const n=s.useContext(r);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function c(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(i):e.components||i:o(e.components),s.createElement(r.Provider,{value:n},e.children)}}}]);