<!doctype html>
<html lang="vi-VN" dir="ltr" class="plugin-pages plugin-id-default" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.5.2">
<title data-rh="true">DataTP Cloud Documentation | DataTP Cloud Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:url" content="https://docs.beelogistics.cloud/"><meta data-rh="true" property="og:locale" content="vi_VN"><meta data-rh="true" property="og:locale:alternate" content="en_GB"><meta data-rh="true" name="docusaurus_locale" content="vi"><meta data-rh="true" name="docusaurus_tag" content="default"><meta data-rh="true" name="docsearch:language" content="vi"><meta data-rh="true" name="docsearch:docusaurus_tag" content="default"><meta data-rh="true" property="og:title" content="DataTP Cloud Documentation | DataTP Cloud Documentation"><meta data-rh="true" name="description" content="Description will go into a meta tag in &lt;head /&gt;"><meta data-rh="true" property="og:description" content="Description will go into a meta tag in &lt;head /&gt;"><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://docs.beelogistics.cloud/"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/" hreflang="vi-VN"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/en/" hreflang="en-GB"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/" hreflang="x-default"><script data-rh="true">function insertBanner(){var n=document.createElement("div");n.id="__docusaurus-base-url-issue-banner-container";n.innerHTML='\n<div id="__docusaurus-base-url-issue-banner" style="border: thick solid red; background-color: rgb(255, 230, 179); margin: 20px; padding: 20px; font-size: 20px;">\n   <p style="font-weight: bold; font-size: 30px;">Your Docusaurus site did not load properly.</p>\n   <p>A very common reason is a wrong site <a href="https://docusaurus.io/docs/docusaurus.config.js/#baseUrl" style="font-weight: bold;">baseUrl configuration</a>.</p>\n   <p>Current configured baseUrl = <span style="font-weight: bold; color: red;">/</span>  (default value)</p>\n   <p>We suggest trying baseUrl = <span id="__docusaurus-base-url-issue-banner-suggestion-container" style="font-weight: bold; color: green;"></span></p>\n</div>\n',document.body.prepend(n);var e=document.getElementById("__docusaurus-base-url-issue-banner-suggestion-container"),s=window.location.pathname,o="/"===s.substr(-1)?s:s+"/";e.innerHTML=o}document.addEventListener("DOMContentLoaded",(function(){void 0===window.docusaurus&&insertBanner()}))</script><link rel="stylesheet" href="/assets/css/styles.28471e28.css">
<script src="/assets/js/runtime~main.d0273a33.js" defer="defer"></script>
<script src="/assets/js/main.9b1f15fc.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Nhảy tới nội dung"><a class="skipToContent_pkKc" href="#__docusaurus_skipToContent_fallback">Nhảy tới nội dung</a></div><nav aria-label="Thanh điều hướng" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Đóng - mở thanh điều hướng" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--light_NBvQ"><img src="/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--dark_oqmI"></div></a><a class="navbar__item navbar__link" href="/docs/shared/user/system">User Guides<!-- --></a><a class="navbar__item navbar__link" href="/docs/shared/developer/SETUP">Developer<!-- --></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_e5gE"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>Tiếng Việt<!-- --></a><ul class="dropdown__menu"><li><a href="/" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="vi-VN">Tiếng Việt<!-- --></a></li><li><a href="/en/" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en-GB">English<!-- --></a></li></ul></div><div class="toggle_VLGw colorModeToggle_FIch"><button class="clean-btn toggleButton_ZTlQ toggleButtonDisabled_NvtF" type="button" disabled="" title="Chuyển đổi chế độ sáng và tối (hiện tại chế độ sáng)" aria-label="Chuyển đổi chế độ sáng và tối (hiện tại chế độ sáng)" aria-live="polite"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_GYGu"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_nQ2C"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_qR4N"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper__olq"><header class="hero hero--primary heroBanner_qdFl"><div class="container"><h1 class="hero__title">DataTP Cloud Documentation</h1><p class="hero__subtitle">Documentation for DataTP Projects</p><div class="buttons_AeoN"><a class="button button--secondary button--lg" href="/docs/shared/user/system">Bắt đầu</a><a href="https://beelogistics.cloud" target="_blank" rel="noopener noreferrer" class="button button--outline button--lg button--secondary" style="margin-left:12px">Trang web chính thức</a></div></div></header><main><section class="features_WV7z"><div class="container"><div class="row"><div class="col col--4"><div class="text--center"><svg xmlns="http://www.w3.org/2000/svg" width="1088" height="687.962" viewBox="0 0 1088 687.962" class="featureSvg_NuXO" role="img"><title>Easy to Use</title><g data-name="Group 12"><g data-name="Group 11"><path fill="#3f3d56" d="M961.81 454.442c-5.27 45.15-16.22 81.4-31.25 110.31-20 38.52-54.21 54.04-84.77 70.28a193 193 0 0 1-27.46 11.94c-55.61 19.3-117.85 14.18-166.74 3.99a657 657 0 0 0-104.09-13.16q-14.97-.675-29.97-.67c-15.42.02-293.07 5.29-360.67-131.57-16.69-33.76-28.13-75-32.24-125.27-11.63-142.12 52.29-235.46 134.74-296.47 155.97-115.41 369.76-110.57 523.43 7.88 102.36 78.9 198.2 198.31 179.02 362.74" data-name="Path 83"></path><path fill="#f2f2f2" d="M930.56 564.752c-20 38.52-47.21 64.04-77.77 80.28a193 193 0 0 1-27.46 11.94c-55.61 19.3-117.85 14.18-166.74 3.99a657 657 0 0 0-104.09-13.16q-14.97-.675-29.97-.67-23.13.03-46.25 1.72c-100.17 7.36-253.82-6.43-321.42-143.29L326 177.962l62.95 161.619 20.09 51.59 55.37-75.98L493 275.962l130.2 149.27 36.8-81.27 254.78 207.919 14.21 11.59Z" data-name="Path 84"></path><path d="m302 282.962 26-57 36 83-31-60Z" data-name="Path 85" opacity="0.1"></path><path d="M554.5 647.802q-14.97-.675-29.97-.67l-115.49-255.96Z" data-name="Path 86" opacity="0.1"></path><path d="M464.411 315.191 493 292.962l130 150-132-128Z" data-name="Path 87" opacity="0.1"></path><path d="M852.79 645.032a193 193 0 0 1-27.46 11.94L623.2 425.232Z" data-name="Path 88" opacity="0.1"></path><circle cx="3" cy="3" r="3" fill="#f2f2f2" data-name="Ellipse 11" transform="translate(479 98.962)"></circle><circle cx="3" cy="3" r="3" fill="#f2f2f2" data-name="Ellipse 12" transform="translate(396 201.962)"></circle><circle cx="2" cy="2" r="2" fill="#f2f2f2" data-name="Ellipse 13" transform="translate(600 220.962)"></circle><circle cx="2" cy="2" r="2" fill="#f2f2f2" data-name="Ellipse 14" transform="translate(180 265.962)"></circle><circle cx="2" cy="2" r="2" fill="#f2f2f2" data-name="Ellipse 15" transform="translate(612 96.962)"></circle><circle cx="2" cy="2" r="2" fill="#f2f2f2" data-name="Ellipse 16" transform="translate(736 192.962)"></circle><circle cx="2" cy="2" r="2" fill="#f2f2f2" data-name="Ellipse 17" transform="translate(858 344.962)"></circle><path fill="#f2f2f2" d="M306 121.222h-2.76v-2.76h-1.48v2.76H299v1.478h2.76v2.759h1.48V122.7H306Z" data-name="Path 89"></path><path fill="#f2f2f2" d="M848 424.222h-2.76v-2.76h-1.48v2.76H841v1.478h2.76v2.759h1.48V425.7H848Z" data-name="Path 90"></path><path fill="#3f3d56" d="M1088 613.962c0 16.569-243.557 74-544 74s-544-57.431-544-74 243.557 14 544 14 544-30.568 544-14" data-name="Path 91"></path><path d="M1088 613.962c0 16.569-243.557 74-544 74s-544-57.431-544-74 243.557 14 544 14 544-30.568 544-14" data-name="Path 92" opacity="0.1"></path><ellipse cx="544" cy="30" fill="#3f3d56" data-name="Ellipse 18" rx="544" ry="30" transform="translate(0 583.962)"></ellipse><path fill="#ff6584" d="M568 571.962c0 33.137-14.775 24-33 24s-33 9.137-33-24 33-96 33-96 33 62.863 33 96" data-name="Path 93"></path><path d="M550 584.641c0 15.062-6.716 10.909-15 10.909s-15 4.153-15-10.909 15-43.636 15-43.636 15 28.576 15 43.636" data-name="Path 94" opacity="0.1"></path><rect width="92" height="18" fill="#2f2e41" data-name="Rectangle 97" rx="9" transform="translate(489 604.962)"></rect><rect width="92" height="18" fill="#2f2e41" data-name="Rectangle 98" rx="9" transform="translate(489 586.962)"></rect><path fill="#3f3d56" d="M137 490.528c0 55.343 34.719 100.126 77.626 100.126" data-name="Path 95"></path><path fill="#6c63ff" d="M214.626 590.654c0-55.965 38.745-101.251 86.626-101.251" data-name="Path 96"></path><path fill="#6c63ff" d="M165.125 495.545c0 52.57 22.14 95.109 49.5 95.109" data-name="Path 97"></path><path fill="#3f3d56" d="M214.626 590.654c0-71.511 44.783-129.377 100.126-129.377" data-name="Path 98"></path><path fill="#a8a8a8" d="M198.3 591.36s11.009-.339 14.326-2.7 16.934-5.183 17.757-1.395 16.544 18.844 4.115 18.945-28.879-1.936-32.19-3.953-4.008-10.897-4.008-10.897" data-name="Path 99"></path><path d="M234.716 604.89c-12.429.1-28.879-1.936-32.19-3.953-2.522-1.536-3.527-7.048-3.863-9.591l-.368.014s.7 8.879 4.009 10.9 19.761 4.053 32.19 3.953c3.588-.029 4.827-1.305 4.759-3.2-.498 1.142-1.867 1.855-4.537 1.877" data-name="Path 100" opacity="0.2"></path><path fill="#3f3d56" d="M721.429 527.062c0 38.029 23.857 68.8 53.341 68.8" data-name="Path 101"></path><path fill="#6c63ff" d="M774.769 595.863c0-38.456 26.623-69.575 59.525-69.575" data-name="Path 102"></path><path fill="#6c63ff" d="M740.755 530.509c0 36.124 15.213 65.354 34.014 65.354" data-name="Path 103"></path><path fill="#3f3d56" d="M774.769 595.863c0-49.139 30.773-88.9 68.8-88.9" data-name="Path 104"></path><path fill="#a8a8a8" d="M763.548 596.348s7.565-.233 9.844-1.856 11.636-3.562 12.2-.958 11.368 12.949 2.828 13.018-19.844-1.33-22.119-2.716-2.753-7.488-2.753-7.488" data-name="Path 105"></path><path d="M788.574 605.645c-8.54.069-19.844-1.33-22.119-2.716-1.733-1.056-2.423-4.843-2.654-6.59l-.253.01s.479 6.1 2.755 7.487 13.579 2.785 22.119 2.716c2.465-.02 3.317-.9 3.27-2.2-.343.788-1.283 1.278-3.118 1.293" data-name="Path 106" opacity="0.2"></path><path fill="#a8a8a8" d="M893.813 618.699s11.36-1.729 14.5-4.591 16.89-7.488 18.217-3.667 19.494 17.447 6.633 19.107-30.153 1.609-33.835-.065-5.515-10.784-5.515-10.784" data-name="Path 107"></path><path d="M933.228 628.154c-12.86 1.659-30.153 1.609-33.835-.065-2.8-1.275-4.535-6.858-5.2-9.45l-.379.061s1.833 9.109 5.516 10.783 20.975 1.725 33.835.065c3.712-.479 4.836-1.956 4.529-3.906-.375 1.246-1.703 2.156-4.466 2.512" data-name="Path 108" opacity="0.2"></path><path fill="#a8a8a8" d="M614.26 617.881s9.587-1.459 12.237-3.875 14.255-6.32 15.374-3.095 16.452 14.725 5.6 16.125-25.448 1.358-28.555-.055-4.656-9.1-4.656-9.1" data-name="Path 109"></path><path d="M647.524 625.856c-10.853 1.4-25.448 1.358-28.555-.055-2.367-1.076-3.827-5.788-4.39-7.976l-.32.051s1.547 7.687 4.655 9.1 17.7 1.456 28.555.055c3.133-.4 4.081-1.651 3.822-3.3-.314 1.057-1.435 1.825-3.767 2.125" data-name="Path 110" opacity="0.2"></path><path fill="#a8a8a8" d="M122.389 613.09s7.463-1.136 9.527-3.016 11.1-4.92 11.969-2.409 12.808 11.463 4.358 12.553-19.811 1.057-22.23-.043-3.624-7.085-3.624-7.085" data-name="Path 111"></path><path d="M148.285 619.302c-8.449 1.09-19.811 1.057-22.23-.043-1.842-.838-2.979-4.506-3.417-6.209l-.249.04s1.2 5.984 3.624 7.085 13.781 1.133 22.23.043c2.439-.315 3.177-1.285 2.976-2.566-.246.818-1.119 1.416-2.934 1.65" data-name="Path 112" opacity="0.2"></path><path d="M383.7 601.318c0 30.22-42.124 20.873-93.7 20.873s-93.074 9.347-93.074-20.873 42.118-36.793 93.694-36.793 93.08 6.573 93.08 36.793" data-name="Path 113" opacity="0.1"></path><path fill="#3f3d56" d="M383.7 593.881c0 30.22-42.124 20.873-93.7 20.873s-93.074 9.347-93.074-20.873 42.114-36.8 93.69-36.8 93.084 6.576 93.084 36.8" data-name="Path 114"></path></g><path fill="#fff" fill-rule="evenodd" d="M360.175 475.732h91.791v37.153h-91.791Z" data-name="Path 40"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M277.126 597.026a21.83 21.83 0 0 1-18.908-10.927 21.829 21.829 0 0 0 18.908 32.782h21.855v-21.855Z" data-name="Path 41"></path><path fill="#3ecc5f" fill-rule="evenodd" d="m375.451 481.607 76.514-4.782v-10.928a21.854 21.854 0 0 0-21.855-21.855h-98.347l-2.732-4.735a3.154 3.154 0 0 0-5.464 0l-2.732 4.732-2.732-4.732a3.154 3.154 0 0 0-5.464 0l-2.732 4.732-2.731-4.732a3.154 3.154 0 0 0-5.464 0l-2.732 4.735h-.071l-4.526-4.525a3.153 3.153 0 0 0-5.276 1.414l-1.5 5.577-5.674-1.521a3.154 3.154 0 0 0-3.863 3.864l1.52 5.679-5.575 1.494a3.155 3.155 0 0 0-1.416 5.278l4.526 4.526v.07l-4.735 2.731a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.732a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.731a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.727a3.154 3.154 0 0 0 0 5.464l4.735 2.736-4.735 2.732a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.732a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.731a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.732a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.731a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.731a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.735a3.154 3.154 0 0 0 0 5.464l4.732 2.732-4.732 2.728a3.154 3.154 0 0 0 0 5.464l4.732 2.732a21.854 21.854 0 0 0 21.858 21.855h131.13a21.854 21.854 0 0 0 21.855-21.855v-87.42l-76.514-4.782a11.632 11.632 0 0 1 0-23.219" data-name="Path 42"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M408.255 618.882h32.782v-43.71h-32.782Z" data-name="Path 43"></path><path fill="#44d860" fill-rule="evenodd" d="M462.893 591.563a5 5 0 0 0-.7.07c-.042-.164-.081-.329-.127-.493a5.457 5.457 0 1 0-5.4-9.372q-.181-.185-.366-.367a5.454 5.454 0 1 0-9.384-5.4c-.162-.046-.325-.084-.486-.126a5.467 5.467 0 1 0-10.788 0c-.162.042-.325.08-.486.126a5.457 5.457 0 1 0-9.384 5.4 21.843 21.843 0 1 0 36.421 21.02 5.452 5.452 0 1 0 .7-10.858" data-name="Path 44"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M419.183 553.317h32.782v-21.855h-32.782Z" data-name="Path 45"></path><path fill="#44d860" fill-rule="evenodd" d="M462.893 545.121a2.732 2.732 0 1 0 0-5.464 3 3 0 0 0-.349.035c-.022-.082-.04-.164-.063-.246a2.733 2.733 0 0 0-1.052-5.253 2.7 2.7 0 0 0-1.648.566q-.09-.093-.184-.184a2.7 2.7 0 0 0 .553-1.633 2.732 2.732 0 0 0-5.245-1.07 10.928 10.928 0 1 0 0 21.031 2.732 2.732 0 0 0 5.245-1.07 2.7 2.7 0 0 0-.553-1.633q.093-.09.184-.184a2.7 2.7 0 0 0 1.648.566 2.732 2.732 0 0 0 1.052-5.253q.033-.122.063-.246a3 3 0 0 0 .349.035" data-name="Path 46"></path><path fill-rule="evenodd" d="M320.836 479.556a2.73 2.73 0 0 1-2.732-2.732 8.2 8.2 0 0 0-16.391 0 2.732 2.732 0 0 1-5.464 0 13.66 13.66 0 0 1 27.319 0 2.73 2.73 0 0 1-2.732 2.732" data-name="Path 47"></path><path fill="#ffff50" fill-rule="evenodd" d="M364.546 618.881h65.565a21.854 21.854 0 0 0 21.855-21.855v-76.492h-65.565a21.854 21.854 0 0 0-21.855 21.855Z" data-name="Path 48"></path><path fill-rule="evenodd" d="M435.596 554.41h-54.681a1.093 1.093 0 1 1 0-2.185h54.681a1.093 1.093 0 0 1 0 2.185m0 21.855h-54.681a1.093 1.093 0 1 1 0-2.186h54.681a1.093 1.093 0 0 1 0 2.186m0 21.855h-54.681a1.093 1.093 0 1 1 0-2.185h54.681a1.093 1.093 0 0 1 0 2.185m0-54.434h-54.681a1.093 1.093 0 1 1 0-2.185h54.681a1.093 1.093 0 0 1 0 2.185m0 21.652h-54.681a1.093 1.093 0 1 1 0-2.186h54.681a1.093 1.093 0 0 1 0 2.186m0 21.855h-54.681a1.093 1.093 0 1 1 0-2.186h54.681a1.093 1.093 0 0 1 0 2.186m16.369-100.959c-.013 0-.024-.007-.037-.005-3.377.115-4.974 3.492-6.384 6.472-1.471 3.114-2.608 5.139-4.473 5.078-2.064-.074-3.244-2.406-4.494-4.874-1.436-2.835-3.075-6.049-6.516-5.929-3.329.114-4.932 3.053-6.346 5.646-1.5 2.762-2.529 4.442-4.5 4.364-2.106-.076-3.225-1.972-4.52-4.167-1.444-2.443-3.112-5.191-6.487-5.1-3.272.113-4.879 2.606-6.3 4.808-1.5 2.328-2.552 3.746-4.551 3.662-2.156-.076-3.27-1.65-4.558-3.472-1.447-2.047-3.077-4.363-6.442-4.251-3.2.109-4.807 2.153-6.224 3.954-1.346 1.709-2.4 3.062-4.621 2.977a1.094 1.094 0 0 0-.079 2.186c3.3.11 4.967-1.967 6.417-3.81 1.286-1.635 2.4-3.045 4.582-3.12 2.1-.09 3.091 1.218 4.584 3.327 1.417 2 3.026 4.277 6.263 4.394 3.391.114 5.022-2.42 6.467-4.663 1.292-2 2.406-3.734 4.535-3.807 1.959-.073 3.026 1.475 4.529 4.022 1.417 2.4 3.023 5.121 6.324 5.241 3.415.118 5.064-2.863 6.5-5.5 1.245-2.282 2.419-4.437 4.5-4.509 1.959-.046 2.981 1.743 4.492 4.732 1.412 2.79 3.013 5.95 6.365 6.071h.185c3.348 0 4.937-3.36 6.343-6.331 1.245-2.634 2.423-5.114 4.444-5.216Z" data-name="Path 49"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M342.691 618.882h43.71v-43.71h-43.71Z" data-name="Path 50"></path><g data-name="Group 8" transform="rotate(-14.98 2188.845 -1120.376)"><rect width="92.361" height="36.462" fill="#d8d8d8" data-name="Rectangle 3" rx="2"></rect><g fill="#4a4a4a" data-name="Group 2" transform="translate(1.531 23.03)"><rect width="5.336" height="5.336" data-name="Rectangle 4" rx="1" transform="translate(16.797)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 5" rx="1" transform="translate(23.12)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 6" rx="1" transform="translate(29.444)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 7" rx="1" transform="translate(35.768)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 8" rx="1" transform="translate(42.091)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 9" rx="1" transform="translate(48.415)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 10" rx="1" transform="translate(54.739)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 11" rx="1" transform="translate(61.063)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 12" rx="1" transform="translate(67.386)"></rect><path fill-rule="evenodd" d="M1.093 0h13.425a1.093 1.093 0 0 1 1.093 1.093v3.15a1.093 1.093 0 0 1-1.093 1.093H1.093A1.093 1.093 0 0 1 0 4.243v-3.15A1.093 1.093 0 0 1 1.093 0M75 0h13.426a1.093 1.093 0 0 1 1.093 1.093v3.15a1.093 1.093 0 0 1-1.093 1.093H75a1.093 1.093 0 0 1-1.093-1.093v-3.15A1.093 1.093 0 0 1 75 0" data-name="Path 51"></path></g><g fill="#4a4a4a" data-name="Group 3" transform="translate(1.531 10.261)"><path fill-rule="evenodd" d="M1.093 0h5.125A1.093 1.093 0 0 1 7.31 1.093v3.149a1.093 1.093 0 0 1-1.092 1.093H1.093A1.093 1.093 0 0 1 0 4.242V1.093A1.093 1.093 0 0 1 1.093 0" data-name="Path 52"></path><rect width="5.336" height="5.336" data-name="Rectangle 13" rx="1" transform="translate(8.299)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 14" rx="1" transform="translate(14.623)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 15" rx="1" transform="translate(20.947)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 16" rx="1" transform="translate(27.271)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 17" rx="1" transform="translate(33.594)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 18" rx="1" transform="translate(39.918)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 19" rx="1" transform="translate(46.242)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 20" rx="1" transform="translate(52.565)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 21" rx="1" transform="translate(58.888)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 22" rx="1" transform="translate(65.212)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 23" rx="1" transform="translate(71.536)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 24" rx="1" transform="translate(77.859)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 25" rx="1" transform="translate(84.183)"></rect></g><g fill="#4a4a4a" data-name="Group 4" transform="rotate(180 45.525 4.773)"><path fill-rule="evenodd" d="M1.093 0h5.126a1.093 1.093 0 0 1 1.093 1.093v3.15a1.093 1.093 0 0 1-1.093 1.093H1.093A1.093 1.093 0 0 1 0 4.243v-3.15A1.093 1.093 0 0 1 1.093 0" data-name="Path 53"></path><rect width="5.336" height="5.336" data-name="Rectangle 26" rx="1" transform="translate(8.299)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 27" rx="1" transform="translate(14.623)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 28" rx="1" transform="translate(20.947)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 29" rx="1" transform="translate(27.271)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 30" rx="1" transform="translate(33.594)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 31" rx="1" transform="translate(39.918)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 32" rx="1" transform="translate(46.242)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 33" rx="1" transform="translate(52.565)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 34" rx="1" transform="translate(58.889)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 35" rx="1" transform="translate(65.213)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 36" rx="1" transform="translate(71.537)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 37" rx="1" transform="translate(77.86)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 38" rx="1" transform="translate(84.183)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 39" rx="1" transform="translate(8.299)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 40" rx="1" transform="translate(14.623)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 41" rx="1" transform="translate(20.947)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 42" rx="1" transform="translate(27.271)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 43" rx="1" transform="translate(33.594)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 44" rx="1" transform="translate(39.918)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 45" rx="1" transform="translate(46.242)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 46" rx="1" transform="translate(52.565)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 47" rx="1" transform="translate(58.889)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 48" rx="1" transform="translate(65.213)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 49" rx="1" transform="translate(71.537)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 50" rx="1" transform="translate(77.86)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 51" rx="1" transform="translate(84.183)"></rect></g><g fill="#4a4a4a" data-name="Group 6"><path fill-rule="evenodd" d="M2.624 16.584h7.3a1.093 1.093 0 0 1 1.092 1.093v3.15a1.093 1.093 0 0 1-1.093 1.093h-7.3a1.093 1.093 0 0 1-1.092-1.093v-3.149a1.093 1.093 0 0 1 1.093-1.094" data-name="Path 54"></path><g data-name="Group 5" transform="translate(12.202 16.584)"><rect width="5.336" height="5.336" data-name="Rectangle 52" rx="1"></rect><rect width="5.336" height="5.336" data-name="Rectangle 53" rx="1" transform="translate(6.324)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 54" rx="1" transform="translate(12.647)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 55" rx="1" transform="translate(18.971)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 56" rx="1" transform="translate(25.295)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 57" rx="1" transform="translate(31.619)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 58" rx="1" transform="translate(37.942)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 59" rx="1" transform="translate(44.265)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 60" rx="1" transform="translate(50.589)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 61" rx="1" transform="translate(56.912)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 62" rx="1" transform="translate(63.236)"></rect></g><path fill-rule="evenodd" d="M83.053 16.584h6.906a1.093 1.093 0 0 1 1.091 1.093v3.15a1.093 1.093 0 0 1-1.091 1.093h-6.907a1.093 1.093 0 0 1-1.093-1.093v-3.149a1.093 1.093 0 0 1 1.093-1.094Z" data-name="Path 55"></path></g><g fill="#4a4a4a" data-name="Group 7" transform="translate(1.531 29.627)"><rect width="5.336" height="5.336" data-name="Rectangle 63" rx="1"></rect><rect width="5.336" height="5.336" data-name="Rectangle 64" rx="1" transform="translate(6.324)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 65" rx="1" transform="translate(12.647)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 66" rx="1" transform="translate(18.971)"></rect><path fill-rule="evenodd" d="M26.387 0h30.422a1.093 1.093 0 0 1 1.093 1.093v3.151a1.093 1.093 0 0 1-1.093 1.093H26.387a1.093 1.093 0 0 1-1.093-1.093V1.093A1.093 1.093 0 0 1 26.387 0m33.594 0h3.942a1.093 1.093 0 0 1 1.093 1.093v3.151a1.093 1.093 0 0 1-1.093 1.093h-3.942a1.093 1.093 0 0 1-1.093-1.093V1.093A1.093 1.093 0 0 1 59.981 0" data-name="Path 56"></path><rect width="5.336" height="5.336" data-name="Rectangle 67" rx="1" transform="translate(66.003)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 68" rx="1" transform="translate(72.327)"></rect><rect width="5.336" height="5.336" data-name="Rectangle 69" rx="1" transform="translate(84.183)"></rect><path d="M78.254 2.273v-1.18A1.093 1.093 0 0 1 79.347 0h3.15a1.093 1.093 0 0 1 1.093 1.093v1.18Z" data-name="Path 57"></path><path d="M83.591 3.063v1.18a1.093 1.093 0 0 1-1.093 1.093h-3.15a1.093 1.093 0 0 1-1.093-1.093v-1.18Z" data-name="Path 58"></path></g><rect width="88.927" height="2.371" fill="#4a4a4a" data-name="Rectangle 70" rx="1.085" transform="translate(1.925 1.17)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 71" opacity="0.136" rx="0.723" transform="translate(4.1 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 72" opacity="0.136" rx="0.723" transform="translate(10.923 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 73" opacity="0.136" rx="0.723" transform="translate(16.173 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 74" opacity="0.136" rx="0.723" transform="translate(21.421 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 75" opacity="0.136" rx="0.723" transform="translate(26.671 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 76" opacity="0.136" rx="0.723" transform="translate(33.232 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 77" opacity="0.136" rx="0.723" transform="translate(38.48 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 78" opacity="0.136" rx="0.723" transform="translate(43.73 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 79" opacity="0.136" rx="0.723" transform="translate(48.978 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 80" opacity="0.136" rx="0.723" transform="translate(55.54 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 81" opacity="0.136" rx="0.723" transform="translate(60.788 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 82" opacity="0.136" rx="0.723" transform="translate(66.038 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 83" opacity="0.136" rx="0.723" transform="translate(72.599 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 84" opacity="0.136" rx="0.723" transform="translate(77.847 1.566)"></rect><rect width="4.986" height="1.581" fill="#d8d8d8" data-name="Rectangle 85" opacity="0.136" rx="0.723" transform="translate(83.097 1.566)"></rect></g><path fill="#44d860" fill-rule="evenodd" d="M408.256 591.563a5.4 5.4 0 0 0-.7.07c-.042-.164-.081-.329-.127-.493a5.457 5.457 0 1 0-5.4-9.372q-.181-.185-.366-.367a5.454 5.454 0 1 0-9.384-5.4c-.162-.046-.325-.084-.486-.126a5.467 5.467 0 1 0-10.788 0c-.162.042-.325.08-.486.126a5.457 5.457 0 1 0-9.384 5.4 21.843 21.843 0 1 0 36.421 21.02 5.452 5.452 0 1 0 .7-10.858" data-name="Path 59"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M342.691 553.317h43.71v-21.855h-43.71Z" data-name="Path 60"></path><path fill="#44d860" fill-rule="evenodd" d="M397.328 545.121a2.732 2.732 0 1 0 0-5.464 3 3 0 0 0-.349.035c-.022-.082-.04-.164-.063-.246a2.733 2.733 0 0 0-1.052-5.253 2.7 2.7 0 0 0-1.648.566q-.09-.093-.184-.184a2.7 2.7 0 0 0 .553-1.633 2.732 2.732 0 0 0-5.245-1.07 10.928 10.928 0 1 0 0 21.031 2.732 2.732 0 0 0 5.245-1.07 2.7 2.7 0 0 0-.553-1.633q.093-.09.184-.184a2.7 2.7 0 0 0 1.648.566 2.732 2.732 0 0 0 1.052-5.253q.033-.122.063-.246a3 3 0 0 0 .349.035" data-name="Path 61"></path><path fill-rule="evenodd" d="M408.256 464.531a3 3 0 0 1-.535-.055 2.8 2.8 0 0 1-.514-.153 3 3 0 0 1-.471-.251 4 4 0 0 1-.415-.339 3 3 0 0 1-.338-.415 2.7 2.7 0 0 1-.459-1.517 3 3 0 0 1 .055-.535 3 3 0 0 1 .152-.514 3 3 0 0 1 .252-.47 2.6 2.6 0 0 1 .753-.754 3 3 0 0 1 .471-.251 2.8 2.8 0 0 1 .514-.153 2.5 2.5 0 0 1 1.071 0 2.7 2.7 0 0 1 .983.4 4 4 0 0 1 .415.339 4 4 0 0 1 .339.415 3 3 0 0 1 .251.47 2.9 2.9 0 0 1 .208 1.049 2.77 2.77 0 0 1-.8 1.934 4 4 0 0 1-.415.339 2.72 2.72 0 0 1-1.519.459m21.855-1.366a2.8 2.8 0 0 1-1.935-.8 4 4 0 0 1-.338-.415 2.7 2.7 0 0 1-.459-1.519 2.8 2.8 0 0 1 .8-1.934 4 4 0 0 1 .415-.339 3 3 0 0 1 .471-.251 2.8 2.8 0 0 1 .514-.153 2.5 2.5 0 0 1 1.071 0 2.7 2.7 0 0 1 .983.4 4 4 0 0 1 .415.339 2.8 2.8 0 0 1 .8 1.934 3 3 0 0 1-.055.535 3 3 0 0 1-.153.514 4 4 0 0 1-.251.47 4 4 0 0 1-.339.415 4 4 0 0 1-.415.339 2.72 2.72 0 0 1-1.519.459" data-name="Path 62"></path></g></svg></div><div class="text--center padding-horiz--md"><h3>Easy to Use</h3><p>Docusaurus was designed from the ground up to be easily installed and used to get your website up and running quickly.<!-- --></p></div></div><div class="col col--4"><div class="text--center"><svg xmlns="http://www.w3.org/2000/svg" width="1129" height="663" viewBox="0 0 1129 663" class="featureSvg_NuXO" role="img"><title>Focus on What Matters</title><circle cx="321" cy="321" r="321" fill="#f2f2f2"></circle><ellipse cx="559" cy="635.5" fill="#3f3d56" rx="514" ry="27.5"></ellipse><ellipse cx="558" cy="627" opacity="0.2" rx="460" ry="22"></ellipse><path fill="#3f3d56" d="M131 152.5h840v50H131z"></path><path fill="#3f3d56" d="M131 608.83a21.67 21.67 0 0 0 21.67 21.67h796.66A21.67 21.67 0 0 0 971 608.83V177.5H131ZM949.33 117.5H152.67A21.67 21.67 0 0 0 131 139.17v38.33h840v-38.33a21.67 21.67 0 0 0-21.67-21.67"></path><path d="M949.33 117.5H152.67A21.67 21.67 0 0 0 131 139.17v38.33h840v-38.33a21.67 21.67 0 0 0-21.67-21.67" opacity="0.2"></path><circle cx="181" cy="147.5" r="13" fill="#3f3d56"></circle><circle cx="217" cy="147.5" r="13" fill="#3f3d56"></circle><circle cx="253" cy="147.5" r="13" fill="#3f3d56"></circle><rect width="337" height="386" x="168" y="213.5" fill="#606060" rx="5.335"></rect><rect width="284" height="22" x="603" y="272.5" fill="#2e8555" rx="5.476"></rect><rect width="416" height="15" x="537" y="352.5" fill="#2e8555" rx="5.476"></rect><rect width="416" height="15" x="537" y="396.5" fill="#2e8555" rx="5.476"></rect><rect width="416" height="15" x="537" y="440.5" fill="#2e8555" rx="5.476"></rect><rect width="416" height="15" x="537" y="484.5" fill="#2e8555" rx="5.476"></rect><rect width="88" height="26" x="865" y="552.5" fill="#3ecc5f" rx="7.028"></rect><path fill="#3f3d56" d="M1053.103 506.116a30.1 30.1 0 0 0 3.983-15.266c0-13.797-8.544-24.98-19.083-24.98s-19.082 11.183-19.082 24.98a30.1 30.1 0 0 0 3.983 15.266 31.25 31.25 0 0 0 0 30.532 31.25 31.25 0 0 0 0 30.532 31.25 31.25 0 0 0 0 30.532 30.1 30.1 0 0 0-3.983 15.266c0 13.797 8.543 24.981 19.082 24.981s19.083-11.184 19.083-24.98a30.1 30.1 0 0 0-3.983-15.267 31.25 31.25 0 0 0 0-30.532 31.25 31.25 0 0 0 0-30.532 31.25 31.25 0 0 0 0-30.532"></path><ellipse cx="1038.003" cy="460.318" fill="#3f3d56" rx="19.083" ry="24.981"></ellipse><ellipse cx="1038.003" cy="429.786" fill="#3f3d56" rx="19.083" ry="24.981"></ellipse><path fill="#3ecc5f" fill-rule="evenodd" d="M1109.439 220.845a92 92 0 0 0 7.106-10.461l-50.14-8.235 54.228.403a91.57 91.57 0 0 0 1.746-72.426l-72.755 37.742 67.097-49.321A91.413 91.413 0 1 0 965.75 220.845a91.5 91.5 0 0 0-10.425 16.67l65.087 33.814-69.4-23.292a91.46 91.46 0 0 0 14.738 85.837 91.406 91.406 0 1 0 143.689 0 91.42 91.42 0 0 0 0-113.03"></path><path d="M946.189 277.36a91 91 0 0 0 19.56 56.514 91.406 91.406 0 1 0 143.69 0c12.25-15.553-163.25-66.774-163.25-56.515" opacity="0.1"></path><path fill="#fff" fill-rule="evenodd" d="M330.12 342.936h111.474v45.12H330.12Z"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M229.263 490.241a26.51 26.51 0 0 1-22.963-13.27 26.51 26.51 0 0 0 22.963 39.812h26.541V490.24Z"></path><path fill="#3ecc5f" fill-rule="evenodd" d="m348.672 350.07 92.922-5.807v-13.27a26.54 26.54 0 0 0-26.541-26.542H295.616l-3.318-5.746a3.83 3.83 0 0 0-6.635 0l-3.318 5.746-3.317-5.746a3.83 3.83 0 0 0-6.636 0l-3.317 5.746-3.318-5.746a3.83 3.83 0 0 0-6.635 0l-3.318 5.746c-.03 0-.056.004-.086.004l-5.497-5.495a3.83 3.83 0 0 0-6.407 1.717l-1.817 6.773-6.89-1.847a3.83 3.83 0 0 0-4.691 4.693l1.844 6.891-6.77 1.814a3.832 3.832 0 0 0-1.72 6.41l5.497 5.497q-.002.041-.004.085l-5.747 3.317a3.83 3.83 0 0 0 0 6.636l5.747 3.317-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318-5.747 3.317a3.83 3.83 0 0 0 0 6.636l5.747 3.317-5.747 3.318a3.83 3.83 0 0 0 0 6.636l5.747 3.317-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318-5.747 3.317a3.83 3.83 0 0 0 0 6.636l5.747 3.317-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318-5.747 3.317a3.83 3.83 0 0 0 0 6.636l5.747 3.317-5.747 3.318a3.83 3.83 0 0 0 0 6.635l5.747 3.318a26.54 26.54 0 0 0 26.541 26.542h159.249a26.54 26.54 0 0 0 26.541-26.542V384.075l-92.922-5.807a14.126 14.126 0 0 1 0-28.197"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M388.511 516.783h39.812V463.7h-39.812Z"></path><path fill="#44d860" fill-rule="evenodd" d="M454.865 483.606a7 7 0 0 0-.848.085q-.073-.3-.154-.599a6.627 6.627 0 1 0-6.557-11.382q-.22-.225-.445-.446a6.624 6.624 0 1 0-11.397-6.564c-.196-.055-.394-.102-.59-.152a6.64 6.64 0 1 0-13.101 0c-.197.05-.394.097-.59.152a6.628 6.628 0 1 0-11.398 6.564 26.528 26.528 0 1 0 44.232 25.528 6.621 6.621 0 1 0 .848-13.186"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M401.782 437.158h39.812v-26.541h-39.812Z"></path><path fill="#44d860" fill-rule="evenodd" d="M454.865 427.205a3.318 3.318 0 0 0 0-6.635 3 3 0 0 0-.424.042c-.026-.1-.049-.199-.077-.298a3.319 3.319 0 0 0-1.278-6.38 3.28 3.28 0 0 0-2 .688q-.11-.113-.224-.223a3.3 3.3 0 0 0 .672-1.983 3.318 3.318 0 0 0-6.37-1.299 13.27 13.27 0 1 0 0 25.541 3.318 3.318 0 0 0 6.37-1.3 3.3 3.3 0 0 0-.672-1.982q.114-.11.223-.223a3.28 3.28 0 0 0 2.001.688 3.318 3.318 0 0 0 1.278-6.38c.028-.098.05-.199.077-.298a3 3 0 0 0 .424.042"></path><path fill-rule="evenodd" d="M282.345 347.581a3.32 3.32 0 0 1-3.317-3.318 9.953 9.953 0 1 0-19.906 0 3.318 3.318 0 1 1-6.636 0 16.588 16.588 0 1 1 33.177 0 3.32 3.32 0 0 1-3.318 3.318"></path><path fill="#ffff50" fill-rule="evenodd" d="M335.428 516.783h79.625a26.54 26.54 0 0 0 26.541-26.542v-92.895H361.97a26.54 26.54 0 0 0-26.542 26.542Z"></path><path fill-rule="evenodd" d="M421.714 438.485h-66.406a1.327 1.327 0 0 1 0-2.654h66.406a1.327 1.327 0 0 1 0 2.654m0 26.542h-66.406a1.327 1.327 0 1 1 0-2.654h66.406a1.327 1.327 0 0 1 0 2.654m0 26.541h-66.406a1.327 1.327 0 1 1 0-2.654h66.406a1.327 1.327 0 0 1 0 2.654m0-66.106h-66.406a1.327 1.327 0 0 1 0-2.655h66.406a1.327 1.327 0 0 1 0 2.655m0 26.294h-66.406a1.327 1.327 0 0 1 0-2.654h66.406a1.327 1.327 0 0 1 0 2.654m0 26.542h-66.406a1.327 1.327 0 0 1 0-2.655h66.406a1.327 1.327 0 0 1 0 2.655m19.88-122.607c-.016 0-.03-.008-.045-.007-4.1.14-6.04 4.241-7.753 7.86-1.786 3.783-3.168 6.242-5.432 6.167-2.506-.09-3.94-2.922-5.458-5.918-1.744-3.443-3.734-7.347-7.913-7.201-4.042.138-5.99 3.708-7.706 6.857-1.828 3.355-3.071 5.394-5.47 5.3-2.557-.093-3.916-2.395-5.488-5.06-1.753-2.967-3.78-6.304-7.878-6.19-3.973.137-5.925 3.166-7.648 5.84-1.822 2.826-3.098 4.549-5.527 4.447-2.618-.093-3.97-2.004-5.535-4.216-1.757-2.486-3.737-5.3-7.823-5.163-3.886.133-5.838 2.615-7.56 4.802-1.634 2.075-2.91 3.718-5.611 3.615a1.328 1.328 0 1 0-.096 2.654c4.004.134 6.032-2.389 7.793-4.628 1.562-1.985 2.91-3.698 5.564-3.789 2.556-.108 3.754 1.48 5.567 4.041 1.721 2.434 3.675 5.195 7.606 5.337 4.118.138 6.099-2.94 7.853-5.663 1.569-2.434 2.923-4.535 5.508-4.624 2.38-.088 3.674 1.792 5.5 4.885 1.722 2.916 3.671 6.22 7.68 6.365 4.147.143 6.15-3.477 7.895-6.682 1.511-2.77 2.938-5.388 5.466-5.475 2.38-.056 3.62 2.116 5.456 5.746 1.714 3.388 3.658 7.226 7.73 7.373l.224.004c4.066 0 5.996-4.08 7.704-7.689 1.511-3.198 2.942-6.21 5.397-6.334Z"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M308.887 516.783h53.083V463.7h-53.083Z"></path><path fill="#44d860" fill-rule="evenodd" d="M388.511 483.606a7 7 0 0 0-.848.085c-.05-.2-.098-.4-.154-.599a6.627 6.627 0 1 0-6.557-11.382q-.22-.225-.444-.446a6.624 6.624 0 1 0-11.397-6.564c-.197-.055-.394-.102-.59-.152a6.64 6.64 0 1 0-13.102 0c-.196.05-.394.097-.59.152a6.628 6.628 0 1 0-11.397 6.564 26.528 26.528 0 1 0 44.231 25.528 6.621 6.621 0 1 0 .848-13.186"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M308.887 437.158h53.083v-26.541h-53.083Z"></path><path fill="#44d860" fill-rule="evenodd" d="M375.24 427.205a3.318 3.318 0 1 0 0-6.635 3 3 0 0 0-.423.042q-.038-.15-.077-.298a3.319 3.319 0 0 0-1.278-6.38 3.28 3.28 0 0 0-2.001.688q-.11-.113-.223-.223a3.3 3.3 0 0 0 .671-1.983 3.318 3.318 0 0 0-6.37-1.299 13.27 13.27 0 1 0 0 25.541 3.318 3.318 0 0 0 6.37-1.3 3.3 3.3 0 0 0-.671-1.982q.113-.11.223-.223a3.28 3.28 0 0 0 2.001.688 3.318 3.318 0 0 0 1.278-6.38c.028-.098.05-.199.077-.298a3 3 0 0 0 .423.042"></path><path fill-rule="evenodd" d="M388.511 329.334a3.6 3.6 0 0 1-.65-.067 3.3 3.3 0 0 1-.624-.185 3.5 3.5 0 0 1-.572-.306 5 5 0 0 1-.504-.411 4 4 0 0 1-.41-.504 3.28 3.28 0 0 1-.558-1.845 3.6 3.6 0 0 1 .067-.65 4 4 0 0 1 .184-.624 3.5 3.5 0 0 1 .307-.57 3.2 3.2 0 0 1 .914-.916 3.5 3.5 0 0 1 .572-.305 3.3 3.3 0 0 1 .624-.186 3.1 3.1 0 0 1 1.3 0 3.2 3.2 0 0 1 1.195.49 5 5 0 0 1 .504.412 5 5 0 0 1 .411.504 3.4 3.4 0 0 1 .306.571 3.5 3.5 0 0 1 .252 1.274 3.36 3.36 0 0 1-.969 2.349 5 5 0 0 1-.504.411 3.3 3.3 0 0 1-1.845.558m26.542-1.66a3.4 3.4 0 0 1-2.35-.968 5 5 0 0 1-.41-.504 3.28 3.28 0 0 1-.558-1.845 3.39 3.39 0 0 1 .967-2.349 5 5 0 0 1 .505-.411 3.5 3.5 0 0 1 .572-.305 3.3 3.3 0 0 1 .623-.186 3.1 3.1 0 0 1 1.3 0 3.2 3.2 0 0 1 1.195.49 5 5 0 0 1 .504.412 3.4 3.4 0 0 1 .97 2.35 4 4 0 0 1-.067.65 3.4 3.4 0 0 1-.186.623 5 5 0 0 1-.305.57 5 5 0 0 1-.412.505 5 5 0 0 1-.504.412 3.3 3.3 0 0 1-1.844.557"></path></svg></div><div class="text--center padding-horiz--md"><h3>Focus on What Matters</h3><p>Docusaurus lets you focus on your docs, and we&#x27;ll do the chores. Go ahead and move your docs into the <!-- --><code>docs</code> directory.<!-- --></p></div></div><div class="col col--4"><div class="text--center"><svg xmlns="http://www.w3.org/2000/svg" width="1041.277" height="554.141" viewBox="0 0 1041.277 554.141" class="featureSvg_NuXO" role="img"><title>Powered by React</title><g data-name="Group 24"><g data-name="Group 23" transform="translate(-.011 -.035)"><path fill="#f2f2f2" d="M961.48 438.21q-1.74 3.75-3.47 7.4-2.7 5.67-5.33 11.12c-.78 1.61-1.56 3.19-2.32 4.77-8.6 17.57-16.63 33.11-23.45 45.89a73.21 73.21 0 0 1-63.81 38.7l-151.65 1.65h-1.6l-13 .14-11.12.12-34.1.37h-1.38l-17.36.19h-.53l-107 1.16-95.51 1-11.11.12-69 .75h-.08l-44.75.48h-.48l-141.5 1.53-42.33.46a88 88 0 0 1-10.79-.54c-1.22-.14-2.44-.3-3.65-.49a87.38 87.38 0 0 1-51.29-27.54c-18.21-20.03-31.46-43.4-40.36-68.76q-1.93-5.49-3.6-11.12c-30.81-104.15 6.75-238.52 74.35-328.44q4.25-5.64 8.64-11l.07-.08c20.79-25.52 44.1-46.84 68.93-62 44-26.91 92.75-34.49 140.7-11.9 40.57 19.12 78.45 28.11 115.17 30.55 3.71.24 7.42.42 11.11.53 84.23 2.65 163.17-27.7 255.87-47.29 3.69-.78 7.39-1.55 11.12-2.28C763 .54 836.36-6.4 923.6 8.19a189 189 0 0 1 26.76 6.4q5.77 1.86 11.12 4c41.64 16.94 64.35 48.24 74 87.46q1.37 5.46 2.37 11.11c17.11 94.34-33 228.16-76.37 321.05" data-name="Path 299"></path><path d="M497.02 445.61a95 95 0 0 1-1.87 11.12h93.7v-11.12Zm-78.25 62.81 11.11-.09v-27.47c-3.81-.17-7.52-.34-11.11-.52Zm-232.92-62.81v11.12h198.5v-11.12Zm849.68-339.52h-74V18.6q-5.35-2.17-11.12-4v91.49H696.87V13.67c-3.73.73-7.43 1.5-11.12 2.28v90.14H429.88V63.24c-3.69-.11-7.4-.29-11.11-.53v43.38H162.9v-62c-24.83 15.16-48.14 36.48-68.93 62h-.07v.08q-4.4 5.4-8.64 11h8.64v328.44h-83q1.66 5.63 3.6 11.12h79.39v93.62a87 87 0 0 0 12.2 2.79c1.21.19 2.43.35 3.65.49a88 88 0 0 0 10.79.54l42.33-.46v-97h255.91v94.21l11.11-.12v-94.07h255.87v91.36l11.12-.12v-91.24h253.49v4.77c.76-1.58 1.54-3.16 2.32-4.77q2.63-5.45 5.33-11.12 1.73-3.64 3.47-7.4v-321h76.42q-1.01-5.69-2.37-11.12M162.9 445.61V117.17h255.87v328.44Zm267 0V117.17h255.85v328.44Zm520.48 0H696.87V117.17h253.49Z" data-name="Path 300" opacity="0.1"></path><path fill="#65617d" d="M863.09 533.65v13l-151.92 1.4-1.62.03-57.74.53-1.38.02-17.55.15h-.52l-106.98.99-175.61 1.63h-.15l-44.65.42-.48.01-198.4 1.82v-15l46.65-28 93.6-.78 2-.01.66-.01 2-.03 44.94-.37 2.01-.01.64-.01 2-.01 14.41-.12.38-.01 35.55-.3h.29l277.4-2.34 6.79-.05h.68l5.18-.05 37.65-.31 2-.03 1.85-.02h.96l11.71-.09 2.32-.03 3.11-.02 9.75-.09 15.47-.13 2-.02 3.48-.02h.65l74.71-.64Z" data-name="Path 301"></path><path d="M863.09 533.65v13l-151.92 1.4-1.62.03-57.74.53-1.38.02-17.55.15h-.52l-106.98.99-175.61 1.63h-.15l-44.65.42-.48.01-198.4 1.82v-15l46.65-28 93.6-.78 2-.01.66-.01 2-.03 44.94-.37 2.01-.01.64-.01 2-.01 14.41-.12.38-.01 35.55-.3h.29l277.4-2.34 6.79-.05h.68l5.18-.05 37.65-.31 2-.03 1.85-.02h.96l11.71-.09 2.32-.03 3.11-.02 9.75-.09 15.47-.13 2-.02 3.48-.02h.65l74.71-.64Z" data-name="Path 302" opacity="0.2"></path><path fill="#3f3d56" d="M296.1 483.66v24.49a6.13 6.13 0 0 1-3.5 5.54 6 6 0 0 1-2.5.6l-34.9.74a6 6 0 0 1-2.7-.57 6.12 6.12 0 0 1-3.57-5.57v-25.23Z" data-name="Path 303"></path><path d="M296.1 483.66v24.49a6.13 6.13 0 0 1-3.5 5.54 6 6 0 0 1-2.5.6l-34.9.74a6 6 0 0 1-2.7-.57 6.12 6.12 0 0 1-3.57-5.57v-25.23Z" data-name="Path 304" opacity="0.1"></path><path fill="#3f3d56" d="M298.1 483.66v24.49a6.13 6.13 0 0 1-3.5 5.54 6 6 0 0 1-2.5.6l-34.9.74a6 6 0 0 1-2.7-.57 6.12 6.12 0 0 1-3.57-5.57v-25.23Z" data-name="Path 305"></path><path fill="#3f3d56" d="M680.92 483.65h47.17v31.5h-47.17z" data-name="Rectangle 137"></path><path d="M680.92 483.65h47.17v31.5h-47.17z" data-name="Rectangle 138" opacity="0.1"></path><path fill="#3f3d56" d="M678.92 483.65h47.17v31.5h-47.17z" data-name="Rectangle 139"></path><path d="M298.09 483.65v4.97l-47.17 1.26v-6.23Z" data-name="Path 306" opacity="0.1"></path><path fill="#65617d" d="M381.35 312.36v168.2a4 4 0 0 1-3.85 3.95l-191.65 5.1h-.05a4 4 0 0 1-3.95-3.95v-173.3a4 4 0 0 1 3.95-3.95h191.6a4 4 0 0 1 3.95 3.95" data-name="Path 307"></path><path d="M185.85 308.41v181.2h-.05a4 4 0 0 1-3.95-3.95v-173.3a4 4 0 0 1 3.95-3.95Z" data-name="Path 308" opacity="0.1"></path><path fill="#39374d" d="M194.59 319.15h177.5V467.4l-177.5 4Z" data-name="Path 309"></path><path d="M726.09 483.65v6.41l-47.17-1.26v-5.15Z" data-name="Path 310" opacity="0.1"></path><path fill="#65617d" d="M788.35 312.36v173.3a4 4 0 0 1-4 3.95l-191.69-5.1a4 4 0 0 1-3.85-3.95v-168.2a4 4 0 0 1 3.95-3.95h191.6a4 4 0 0 1 3.99 3.95" data-name="Path 311"></path><path d="M788.35 312.36v173.3a4 4 0 0 1-4 3.95v-181.2a4 4 0 0 1 4 3.95" data-name="Path 312" opacity="0.1"></path><path fill="#39374d" d="M775.59 319.15h-177.5V467.4l177.5 4Z" data-name="Path 313"></path><path fill="#65617d" d="M583.85 312.36v168.2a4 4 0 0 1-3.85 3.95l-191.65 5.1a4 4 0 0 1-4-3.95v-173.3a4 4 0 0 1 3.95-3.95h191.6a4 4 0 0 1 3.95 3.95" data-name="Path 314"></path><path fill="#4267b2" d="M397.09 319.15h177.5V467.4l-177.5 4Z" data-name="Path 315"></path><path d="M863.09 533.65v13l-151.92 1.4-1.62.03-57.74.53-1.38.02-17.55.15h-.52l-106.98.99-175.61 1.63h-.15l-44.65.42-.48.01-198.4 1.82v-15l202.51-1.33h.48l40.99-.28h.19l283.08-1.87h.29l.17-.01h.47l4.79-.03h1.46l74.49-.5 4.4-.02.98-.01Z" data-name="Path 316" opacity="0.1"></path><circle cx="51.33" cy="51.33" r="51.33" fill="#fbbebe" data-name="Ellipse 111" transform="translate(435.93 246.82)"></circle><path fill="#fbbebe" d="M538.6 377.16s-99.5 12-90 0c3.44-4.34 4.39-17.2 4.2-31.85-.06-4.45-.22-9.06-.45-13.65-1.1-22-3.75-43.5-3.75-43.5s87-41 77-8.5c-4 13.13-2.69 31.57.35 48.88.89 5.05 1.92 10 3 14.7a345 345 0 0 0 9.65 33.92" data-name="Path 317"></path><path fill="#ff6584" d="M506.13 373.09c11.51-2.13 23.7-6 34.53-1.54 2.85 1.17 5.47 2.88 8.39 3.86s6.12 1.22 9.16 1.91c10.68 2.42 19.34 10.55 24.9 20s8.44 20.14 11.26 30.72l6.9 25.83c6 22.45 12 45.09 13.39 68.3a2438 2438 0 0 1-250.84 1.43c5.44-10.34 11-21.31 10.54-33s-7.19-23.22-4.76-34.74c1.55-7.34 6.57-13.39 9.64-20.22 8.75-19.52 1.94-45.79 17.32-60.65 6.92-6.68 17-9.21 26.63-8.89 12.28.41 24.85 4.24 37 6.11 15.56 2.36 30.26 3.76 45.94.88" data-name="Path 318"></path><path d="m637.03 484.26-.1 1.43v.1l-.17 2.3-1.33 18.51-1.61 22.3-.46 6.28-1 13.44v.17l-107 1-175.59 1.9v.84h-.14v-1.12l.45-14.36.86-28.06.74-23.79.07-2.37a10.53 10.53 0 0 1 11.42-10.17c4.72.4 10.85.89 18.18 1.41l3 .22c42.33 2.94 120.56 6.74 199.5 2 1.66-.09 3.33-.19 5-.31 12.24-.77 24.47-1.76 36.58-3a10.53 10.53 0 0 1 11.6 11.23Z" data-name="Path 319" opacity="0.1"></path><path fill="#3f3d56" d="M349.74 552.53v-.84l175.62-1.91 107-1h.3v-.17l1-13.44.43-6 1.64-22.61 1.29-17.9v-.44a10.6 10.6 0 0 0-.11-2.47.3.3 0 0 0 0-.1 10.4 10.4 0 0 0-2-4.64 10.54 10.54 0 0 0-9.42-4 937 937 0 0 1-36.58 3c-1.67.12-3.34.22-5 .31-78.94 4.69-157.17.89-199.5-2l-3-.22c-7.33-.52-13.46-1-18.18-1.41a10.54 10.54 0 0 0-11.24 8.53 11 11 0 0 0-.18 1.64l-.68 22.16-.93 28.07-.44 14.36v1.12Z" data-name="Path 320"></path><path d="m637.33 491.27-1.23 15.33-1.83 22.85-.46 5.72-1 12.81-.06.64v.17l-.15 1.48.11-1.48h-.29l-107 1-175.65 1.9v-.28l.49-14.36 1-28.06.64-18.65a6.36 6.36 0 0 1 3.06-5.25 6.25 6.25 0 0 1 3.78-.9c2.1.17 4.68.37 7.69.59 4.89.36 10.92.78 17.94 1.22 13 .82 29.31 1.7 48 2.42 52 2 122.2 2.67 188.88-3.17 3-.26 6.1-.55 9.13-.84a6.26 6.26 0 0 1 3.48.66 5 5 0 0 1 .86.54 6.14 6.14 0 0 1 2 2.46 3.6 3.6 0 0 1 .25.61 6.3 6.3 0 0 1 .36 2.59" data-name="Path 321" opacity="0.1"></path><path d="M298.1 504.96v3.19a6.13 6.13 0 0 1-3.5 5.54l-40.1.77a6.12 6.12 0 0 1-3.57-5.57v-3Z" data-name="Path 322" opacity="0.1"></path><path fill="#3f3d56" d="m298.59 515.57-52.25 1v-8.67l52.25-1Z" data-name="Path 323"></path><path d="m298.59 515.57-52.25 1v-8.67l52.25-1Z" data-name="Path 324" opacity="0.1"></path><path fill="#3f3d56" d="m300.59 515.57-52.25 1v-8.67l52.25-1Z" data-name="Path 325"></path><path d="M679.22 506.96v3.19a6.13 6.13 0 0 0 3.5 5.54l40.1.77a6.12 6.12 0 0 0 3.57-5.57v-3Z" data-name="Path 326" opacity="0.1"></path><path d="m678.72 517.57 52.25 1v-8.67l-52.25-1Z" data-name="Path 327" opacity="0.1"></path><path fill="#3f3d56" d="m676.72 517.57 52.25 1v-8.67l-52.25-1Z" data-name="Path 328"></path><path fill="#3f3d56" d="M454.79 313.88c.08 7-3.16 13.6-5.91 20.07a163.5 163.5 0 0 0-12.66 74.71c.73 11 2.58 22 .73 32.9s-8.43 21.77-19 24.9c17.53 10.45 41.26 9.35 57.76-2.66 8.79-6.4 15.34-15.33 21.75-24.11a97.86 97.86 0 0 1-13.31 44.75 103.43 103.43 0 0 0 73.51-40.82c4.31-5.81 8.06-12.19 9.72-19.23 3.09-13-1.22-26.51-4.51-39.5a266 266 0 0 1-6.17-33c-.43-3.56-.78-7.22.1-10.7 1-4.07 3.67-7.51 5.64-11.22 5.6-10.54 5.73-23.3 2.86-34.88s-8.49-22.26-14.06-32.81c-4.46-8.46-9.3-17.31-17.46-22.28-5.1-3.1-11-4.39-16.88-5.64l-25.37-5.43c-5.55-1.19-11.26-2.38-16.87-1.51-9.47 1.48-16.14 8.32-22 15.34-4.59 5.46-15.81 15.71-16.6 22.86-.72 6.59 5.1 17.63 6.09 24.58 1.3 9 2.22 6 7.3 11.52 3.21 3.42 5.28 7.37 5.34 12.16" data-name="Path 329"></path></g><path fill="#fff" fill-rule="evenodd" d="M280.139 370.832h43.635v17.662h-43.635Z" data-name="Path 40"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M240.66 428.493a10.38 10.38 0 0 1-8.989-5.195 10.377 10.377 0 0 0 8.988 15.584h10.391v-10.389Z" data-name="Path 41"></path><path fill="#3ecc5f" fill-rule="evenodd" d="m287.402 373.625 36.373-2.273v-5.195a10.39 10.39 0 0 0-10.39-10.389h-46.75l-1.3-2.249a1.5 1.5 0 0 0-2.6 0l-1.3 2.249-1.3-2.249a1.5 1.5 0 0 0-2.6 0l-1.3 2.249-1.3-2.249a1.5 1.5 0 0 0-2.6 0l-1.3 2.249h-.034l-2.152-2.151a1.5 1.5 0 0 0-2.508.672l-.696 2.653-2.7-.723a1.5 1.5 0 0 0-1.836 1.837l.722 2.7-2.65.71a1.5 1.5 0 0 0-.673 2.509l2.152 2.152v.033l-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.25 1.282-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3-2.249 1.3a1.5 1.5 0 0 0 0 2.6l2.249 1.3a10.39 10.39 0 0 0 10.389 10.34h62.335a10.39 10.39 0 0 0 10.39-10.39v-41.557l-36.373-2.273a5.53 5.53 0 0 1 0-11.038" data-name="Path 42"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M302.996 438.882h15.584v-20.779h-15.584Z" data-name="Path 43"></path><path fill="#44d860" fill-rule="evenodd" d="M328.97 425.895a3 3 0 0 0-.332.033q-.028-.117-.06-.234a2.594 2.594 0 1 0-2.567-4.455q-.086-.088-.174-.175a2.593 2.593 0 1 0-4.461-2.569q-.115-.031-.231-.06a2.6 2.6 0 1 0-5.128 0q-.116.029-.231.06a2.594 2.594 0 1 0-4.461 2.569 10.384 10.384 0 1 0 17.314 9.992 2.592 2.592 0 1 0 .332-5.161" data-name="Path 44"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M308.191 407.713h15.584v-10.389h-15.584Z" data-name="Path 45"></path><path fill="#44d860" fill-rule="evenodd" d="M328.969 403.818a1.3 1.3 0 1 0 0-2.6 1 1 0 0 0-.166.017l-.03-.117a1.3 1.3 0 0 0-.5-2.5 1.3 1.3 0 0 0-.783.269l-.087-.087a1.3 1.3 0 0 0 .263-.776 1.3 1.3 0 0 0-2.493-.509 5.195 5.195 0 1 0 0 10 1.3 1.3 0 0 0 2.493-.509 1.3 1.3 0 0 0-.263-.776l.087-.087a1.3 1.3 0 0 0 .783.269 1.3 1.3 0 0 0 .5-2.5q.016-.058.03-.117a1 1 0 0 0 .166.017" data-name="Path 46"></path><path fill-rule="evenodd" d="M261.439 372.65a1.3 1.3 0 0 1-1.3-1.3 3.9 3.9 0 0 0-7.792 0 1.3 1.3 0 1 1-2.6 0 6.494 6.494 0 0 1 12.987 0 1.3 1.3 0 0 1-1.3 1.3" data-name="Path 47"></path><path fill="#ffff50" fill-rule="evenodd" d="M282.217 438.882h31.168a10.39 10.39 0 0 0 10.389-10.389V392.13h-31.168a10.39 10.39 0 0 0-10.389 10.389Z" data-name="Path 48"></path><path fill-rule="evenodd" d="M315.993 408.233h-25.994a.52.52 0 1 1 0-1.039h25.994a.52.52 0 0 1 0 1.039m0 10.389h-25.994a.52.52 0 1 1 0-1.039h25.994a.52.52 0 0 1 0 1.039m0 10.389h-25.994a.52.52 0 1 1 0-1.039h25.994a.52.52 0 0 1 0 1.039m0-25.877h-25.994a.52.52 0 1 1 0-1.039h25.994a.52.52 0 0 1 0 1.039m0 10.293h-25.994a.52.52 0 1 1 0-1.039h25.994a.52.52 0 0 1 0 1.039m0 10.389h-25.994a.52.52 0 1 1 0-1.039h25.994a.52.52 0 0 1 0 1.039m7.782-47.993h-.018c-1.605.055-2.365 1.66-3.035 3.077-.7 1.48-1.24 2.443-2.126 2.414-.981-.035-1.542-1.144-2.137-2.317-.683-1.347-1.462-2.876-3.1-2.819-1.582.054-2.344 1.451-3.017 2.684-.715 1.313-1.2 2.112-2.141 2.075-1-.036-1.533-.938-2.149-1.981-.686-1.162-1.479-2.467-3.084-2.423-1.555.053-2.319 1.239-2.994 2.286-.713 1.106-1.213 1.781-2.164 1.741-1.025-.036-1.554-.784-2.167-1.65-.688-.973-1.463-2.074-3.062-2.021a3.82 3.82 0 0 0-2.959 1.879c-.64.812-1.14 1.456-2.2 1.415a.52.52 0 0 0-.037 1.039 3.59 3.59 0 0 0 3.05-1.811c.611-.777 1.139-1.448 2.178-1.483 1-.043 1.47.579 2.179 1.582.674.953 1.438 2.033 2.977 2.089 1.612.054 2.387-1.151 3.074-2.217.614-.953 1.144-1.775 2.156-1.81.931-.035 1.438.7 2.153 1.912.674 1.141 1.437 2.434 3.006 2.491 1.623.056 2.407-1.361 3.09-2.616.592-1.085 1.15-2.109 2.14-2.143.931-.022 1.417.829 2.135 2.249.671 1.326 1.432 2.828 3.026 2.886h.088c1.592 0 2.347-1.6 3.015-3.01.592-1.252 1.152-2.431 2.113-2.479Z" data-name="Path 49"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M271.828 438.882h20.779v-20.779h-20.779Z" data-name="Path 50"></path><g data-name="Group 8" transform="rotate(-14.98 1643.944 -873.93)"><rect width="43.906" height="17.333" fill="#d8d8d8" data-name="Rectangle 3" rx="2"></rect><g fill="#4a4a4a" data-name="Group 2" transform="translate(.728 10.948)"><rect width="2.537" height="2.537" data-name="Rectangle 4" rx="1" transform="translate(7.985)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 5" rx="1" transform="translate(10.991)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 6" rx="1" transform="translate(13.997)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 7" rx="1" transform="translate(17.003)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 8" rx="1" transform="translate(20.009)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 9" rx="1" transform="translate(23.015)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 10" rx="1" transform="translate(26.021)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 11" rx="1" transform="translate(29.028)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 12" rx="1" transform="translate(32.034)"></rect><path fill-rule="evenodd" d="M.519 0H6.9a.52.52 0 0 1 .521.52v1.5a.52.52 0 0 1-.519.519H.519A.52.52 0 0 1 0 2.017V.519A.52.52 0 0 1 .519 0m35.134 0h6.383a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519h-6.384a.52.52 0 0 1-.519-.519v-1.5A.52.52 0 0 1 35.652 0Z" data-name="Path 51"></path></g><g fill="#4a4a4a" data-name="Group 3" transform="translate(.728 4.878)"><path fill-rule="evenodd" d="M.519 0h2.437a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519H.519A.52.52 0 0 1 0 2.017V.519A.52.52 0 0 1 .519 0" data-name="Path 52"></path><rect width="2.537" height="2.537" data-name="Rectangle 13" rx="1" transform="translate(3.945)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 14" rx="1" transform="translate(6.951)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 15" rx="1" transform="translate(9.958)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 16" rx="1" transform="translate(12.964)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 17" rx="1" transform="translate(15.97)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 18" rx="1" transform="translate(18.976)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 19" rx="1" transform="translate(21.982)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 20" rx="1" transform="translate(24.988)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 21" rx="1" transform="translate(27.994)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 22" rx="1" transform="translate(31)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 23" rx="1" transform="translate(34.006)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 24" rx="1" transform="translate(37.012)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 25" rx="1" transform="translate(40.018)"></rect></g><g fill="#4a4a4a" data-name="Group 4" transform="rotate(180 21.642 2.269)"><path fill-rule="evenodd" d="M.519 0h2.437a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519H.519A.52.52 0 0 1 0 2.017V.519A.52.52 0 0 1 .519 0" data-name="Path 53"></path><rect width="2.537" height="2.537" data-name="Rectangle 26" rx="1" transform="translate(3.945)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 27" rx="1" transform="translate(6.951)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 28" rx="1" transform="translate(9.958)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 29" rx="1" transform="translate(12.964)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 30" rx="1" transform="translate(15.97)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 31" rx="1" transform="translate(18.976)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 32" rx="1" transform="translate(21.982)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 33" rx="1" transform="translate(24.988)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 34" rx="1" transform="translate(27.994)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 35" rx="1" transform="translate(31.001)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 36" rx="1" transform="translate(34.007)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 37" rx="1" transform="translate(37.013)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 38" rx="1" transform="translate(40.018)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 39" rx="1" transform="translate(3.945)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 40" rx="1" transform="translate(6.951)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 41" rx="1" transform="translate(9.958)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 42" rx="1" transform="translate(12.964)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 43" rx="1" transform="translate(15.97)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 44" rx="1" transform="translate(18.976)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 45" rx="1" transform="translate(21.982)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 46" rx="1" transform="translate(24.988)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 47" rx="1" transform="translate(27.994)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 48" rx="1" transform="translate(31.001)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 49" rx="1" transform="translate(34.007)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 50" rx="1" transform="translate(37.013)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 51" rx="1" transform="translate(40.018)"></rect></g><g fill="#4a4a4a" data-name="Group 6"><path fill-rule="evenodd" d="M1.247 7.883h3.47a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519h-3.47A.52.52 0 0 1 .728 9.9V8.403a.52.52 0 0 1 .519-.52" data-name="Path 54"></path><g data-name="Group 5" transform="translate(5.801 7.883)"><rect width="2.537" height="2.537" data-name="Rectangle 52" rx="1"></rect><rect width="2.537" height="2.537" data-name="Rectangle 53" rx="1" transform="translate(3.006)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 54" rx="1" transform="translate(6.012)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 55" rx="1" transform="translate(9.018)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 56" rx="1" transform="translate(12.025)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 57" rx="1" transform="translate(15.031)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 58" rx="1" transform="translate(18.037)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 59" rx="1" transform="translate(21.042)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 60" rx="1" transform="translate(24.049)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 61" rx="1" transform="translate(27.055)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 62" rx="1" transform="translate(30.061)"></rect></g><path fill-rule="evenodd" d="M39.482 7.883h3.28a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519h-3.281a.52.52 0 0 1-.519-.521V8.403a.52.52 0 0 1 .519-.52Z" data-name="Path 55"></path></g><g fill="#4a4a4a" data-name="Group 7" transform="translate(.728 14.084)"><rect width="2.537" height="2.537" data-name="Rectangle 63" rx="1"></rect><rect width="2.537" height="2.537" data-name="Rectangle 64" rx="1" transform="translate(3.006)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 65" rx="1" transform="translate(6.012)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 66" rx="1" transform="translate(9.018)"></rect><path fill-rule="evenodd" d="M12.543 0h14.462a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519H12.543a.52.52 0 0 1-.519-.52V.519A.52.52 0 0 1 12.543 0m15.97 0h1.874a.52.52 0 0 1 .519.519v1.5a.52.52 0 0 1-.519.519h-1.874a.52.52 0 0 1-.519-.519v-1.5A.52.52 0 0 1 28.513 0" data-name="Path 56"></path><rect width="2.537" height="2.537" data-name="Rectangle 67" rx="1" transform="translate(31.376)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 68" rx="1" transform="translate(34.382)"></rect><rect width="2.537" height="2.537" data-name="Rectangle 69" rx="1" transform="translate(40.018)"></rect><path d="M37.199 1.08V.519A.52.52 0 0 1 37.718 0h1.499a.52.52 0 0 1 .519.519v.561Z" data-name="Path 57"></path><path d="M39.737 1.456v.561a.52.52 0 0 1-.519.519h-1.499a.52.52 0 0 1-.519-.519v-.561Z" data-name="Path 58"></path></g><rect width="42.273" height="1.127" fill="#4a4a4a" data-name="Rectangle 70" rx="0.564" transform="translate(.915 .556)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 71" opacity="0.136" rx="0.376" transform="translate(1.949 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 72" opacity="0.136" rx="0.376" transform="translate(5.193 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 73" opacity="0.136" rx="0.376" transform="translate(7.688 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 74" opacity="0.136" rx="0.376" transform="translate(10.183 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 75" opacity="0.136" rx="0.376" transform="translate(12.679 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 76" opacity="0.136" rx="0.376" transform="translate(15.797 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 77" opacity="0.136" rx="0.376" transform="translate(18.292 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 78" opacity="0.136" rx="0.376" transform="translate(20.788 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 79" opacity="0.136" rx="0.376" transform="translate(23.283 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 80" opacity="0.136" rx="0.376" transform="translate(26.402 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 81" opacity="0.136" rx="0.376" transform="translate(28.897 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 82" opacity="0.136" rx="0.376" transform="translate(31.393 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 83" opacity="0.136" rx="0.376" transform="translate(34.512 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 84" opacity="0.136" rx="0.376" transform="translate(37.007 .744)"></rect><rect width="2.37" height="0.752" fill="#d8d8d8" data-name="Rectangle 85" opacity="0.136" rx="0.376" transform="translate(39.502 .744)"></rect></g><path fill="#44d860" fill-rule="evenodd" d="M302.996 425.895a3 3 0 0 0-.332.033q-.029-.117-.06-.234a2.594 2.594 0 1 0-2.567-4.455q-.086-.088-.174-.175a2.593 2.593 0 1 0-4.461-2.569q-.116-.031-.231-.06a2.6 2.6 0 1 0-5.128 0q-.115.029-.231.06a2.594 2.594 0 1 0-4.461 2.569 10.384 10.384 0 1 0 17.314 9.992 2.592 2.592 0 1 0 .332-5.161" data-name="Path 59"></path><path fill="#3ecc5f" fill-rule="evenodd" d="M271.828 407.713h20.779v-10.389h-20.779Z" data-name="Path 60"></path><path fill="#44d860" fill-rule="evenodd" d="M297.801 403.818a1.3 1.3 0 1 0 0-2.6 1 1 0 0 0-.166.017l-.03-.117a1.3 1.3 0 0 0-.5-2.5 1.3 1.3 0 0 0-.783.269l-.087-.087a1.3 1.3 0 0 0 .263-.776 1.3 1.3 0 0 0-2.493-.509 5.195 5.195 0 1 0 0 10 1.3 1.3 0 0 0 2.493-.509 1.3 1.3 0 0 0-.263-.776l.087-.087a1.3 1.3 0 0 0 .783.269 1.3 1.3 0 0 0 .5-2.5q.016-.058.03-.117a1 1 0 0 0 .166.017" data-name="Path 61"></path><path fill-rule="evenodd" d="M302.997 365.507a1.4 1.4 0 0 1-.255-.026 1.3 1.3 0 0 1-.244-.073 1.4 1.4 0 0 1-.224-.119 2 2 0 0 1-.2-.161 1.5 1.5 0 0 1-.161-.2 1.3 1.3 0 0 1-.218-.722 1.4 1.4 0 0 1 .026-.255 1.5 1.5 0 0 1 .072-.244 1.4 1.4 0 0 1 .12-.223 1.3 1.3 0 0 1 .358-.358 1.4 1.4 0 0 1 .224-.119 1.3 1.3 0 0 1 .244-.073 1.2 1.2 0 0 1 .509 0 1.3 1.3 0 0 1 .468.192 2 2 0 0 1 .2.161 2 2 0 0 1 .161.2 1.3 1.3 0 0 1 .12.223 1.4 1.4 0 0 1 .1.5 1.32 1.32 0 0 1-.379.919 2 2 0 0 1-.2.161 1.4 1.4 0 0 1-.223.119 1.3 1.3 0 0 1-.5.1m10.389-.649a1.33 1.33 0 0 1-.92-.379 2 2 0 0 1-.161-.2 1.3 1.3 0 0 1-.218-.722 1.33 1.33 0 0 1 .379-.919 2 2 0 0 1 .2-.161 1.4 1.4 0 0 1 .224-.119 1.3 1.3 0 0 1 .244-.073 1.2 1.2 0 0 1 .509 0 1.3 1.3 0 0 1 .468.192 2 2 0 0 1 .2.161 1.33 1.33 0 0 1 .379.919 1.5 1.5 0 0 1-.026.255 1.3 1.3 0 0 1-.073.244 2 2 0 0 1-.119.223 2 2 0 0 1-.161.2 2 2 0 0 1-.2.161 1.3 1.3 0 0 1-.722.218" data-name="Path 62"></path><g fill="#61dafb" transform="translate(466.3 278.56)"><path d="M263.668 117.179c0-5.827-7.3-11.35-18.487-14.775 2.582-11.4 1.434-20.477-3.622-23.382a7.86 7.86 0 0 0-4.016-1v4a4.15 4.15 0 0 1 2.044.466c2.439 1.4 3.5 6.724 2.672 13.574-.2 1.685-.52 3.461-.914 5.272a87 87 0 0 0-11.386-1.954 87.5 87.5 0 0 0-7.459-8.965c5.845-5.433 11.332-8.41 15.062-8.41V78c-4.931 0-11.386 3.514-17.913 9.611-6.527-6.061-12.982-9.539-17.913-9.539v4c3.712 0 9.216 2.959 15.062 8.356a85 85 0 0 0-7.405 8.947 84 84 0 0 0-11.4 1.972 54 54 0 0 1-.932-5.2c-.843-6.85.2-12.175 2.618-13.592a4 4 0 0 1 2.062-.466v-4a8 8 0 0 0-4.052 1c-5.039 2.9-6.168 11.96-3.568 23.328-11.153 3.443-18.415 8.947-18.415 14.757 0 5.828 7.3 11.35 18.487 14.775-2.582 11.4-1.434 20.477 3.622 23.382a7.9 7.9 0 0 0 4.034 1c4.931 0 11.386-3.514 17.913-9.611 6.527 6.061 12.982 9.539 17.913 9.539a8 8 0 0 0 4.052-1c5.039-2.9 6.168-11.96 3.568-23.328 11.111-3.42 18.373-8.943 18.373-14.752m-23.346-11.96a80 80 0 0 1-2.421 7.083 83 83 0 0 0-2.349-4.3 97 97 0 0 0-2.582-4.2c2.547.377 5.004.843 7.353 1.417Zm-8.212 19.1c-1.4 2.421-2.833 4.716-4.321 6.85a93 93 0 0 1-8.1.359c-2.708 0-5.415-.126-8.069-.341q-2.232-3.2-4.339-6.814-2.044-3.523-3.73-7.136a94 94 0 0 1 3.712-7.154c1.4-2.421 2.833-4.716 4.321-6.85a93 93 0 0 1 8.1-.359c2.708 0 5.415.126 8.069.341q2.232 3.2 4.339 6.814 2.044 3.523 3.73 7.136a101 101 0 0 1-3.712 7.15Zm5.792-2.331a77 77 0 0 1 2.474 7.136 80 80 0 0 1-7.387 1.434c.879-1.381 1.757-2.8 2.582-4.25a96 96 0 0 0 2.329-4.324Zm-18.182 19.128a74 74 0 0 1-4.985-5.738c1.614.072 3.263.126 4.931.126 1.685 0 3.353-.036 4.985-.126a70 70 0 0 1-4.931 5.738m-13.34-10.561c-2.546-.377-5-.843-7.352-1.417a80 80 0 0 1 2.421-7.083c.735 1.434 1.506 2.869 2.349 4.3s1.702 2.837 2.582 4.2m13.25-37.314a74 74 0 0 1 4.985 5.738 111 111 0 0 0-4.931-.126c-1.686 0-3.353.036-4.985.126a70 70 0 0 1 4.931-5.738M206.362 103.8a101 101 0 0 0-4.913 8.55 77 77 0 0 1-2.474-7.136 90 90 0 0 1 7.387-1.414m-16.227 22.449c-6.348-2.708-10.454-6.258-10.454-9.073s4.106-6.383 10.454-9.073c1.542-.663 3.228-1.255 4.967-1.811a86 86 0 0 0 4.034 10.92 85 85 0 0 0-3.981 10.866 54 54 0 0 1-5.021-1.826Zm9.647 25.623c-2.439-1.4-3.5-6.724-2.672-13.574.2-1.686.52-3.461.914-5.272a87 87 0 0 0 11.386 1.954 87.5 87.5 0 0 0 7.459 8.965c-5.845 5.433-11.332 8.41-15.062 8.41a4.3 4.3 0 0 1-2.026-.48Zm42.532-13.663c.843 6.85-.2 12.175-2.618 13.592a4 4 0 0 1-2.062.466c-3.712 0-9.216-2.959-15.062-8.356a85 85 0 0 0 7.405-8.947 84 84 0 0 0 11.4-1.972 50 50 0 0 1 .936 5.22Zm6.9-11.96c-1.542.663-3.228 1.255-4.967 1.811a86 86 0 0 0-4.034-10.92 85 85 0 0 0 3.981-10.866 57 57 0 0 1 5.039 1.829c6.348 2.708 10.454 6.258 10.454 9.073-.017 2.818-4.123 6.386-10.471 9.076Z" data-name="Path 330"></path><path d="M201.718 78.072" data-name="Path 331"></path><circle cx="8.194" cy="8.194" r="8.194" data-name="Ellipse 112" transform="translate(211.472 108.984)"></circle><path d="M237.525 78.018" data-name="Path 332"></path></g></g></svg></div><div class="text--center padding-horiz--md"><h3>Powered by React</h3><p>Extend or customize your website layout by reusing React. Docusaurus can be extended while reusing the same header and footer.<!-- --></p></div></div></div></div></section></main></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 DataTP Cloud.</div></div></div></footer></div>
</body>
</html>