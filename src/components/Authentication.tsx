import React, { useState, useEffect } from 'react';
import { useHistory } from '@docusaurus/router';

// <PERSON>h sách token hợp lệ (trong thực tế nên lưu ở server)
const VALID_TOKENS = ['dev_token_123', 'manager_token_456'];

export default function Auth({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const history = useHistory();

  useEffect(() => {
    // Ki<PERSON>m tra token trong localStorage
    const token = localStorage.getItem('dev_auth_token');
    if (token && VALID_TOKENS.includes(token)) {
      setIsAuthenticated(true);
    }
  }, []);

  const handleLogin = (e) => {
    e.preventDefault();

    // Đơn giản hóa: password là token
    if (VALID_TOKENS.includes(password)) {
      localStorage.setItem('dev_auth_token', password);
      setIsAuthenticated(true);
      setError('');
    } else {
      setError('Mã xác thực không hợp lệ');
    }
  };

  if (!isAuthenticated) {
    return (
      <div style={{ maxWidth: '400px', margin: '100px auto', padding: '20px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h2>Khu vực dành cho Developer</h2>
        <p>Vui lòng nhập mã xác thực để truy cập nội dung này</p>

        <form onSubmit={handleLogin}>
          <div style={{ marginBottom: '15px' }}>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Nhập mã xác thực"
              style={{ width: '100%', padding: '8px' }}
            />
          </div>

          {error && <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>}

          <button
            type="submit"
            style={{ background: '#2e8555', color: 'white', border: 'none', padding: '10px 15px', borderRadius: '4px', cursor: 'pointer' }}
          >
            Xác thực
          </button>
        </form>
      </div>
    );
  }

  return <>{children}</>;
}