import React from 'react';
import Layout from '@theme/Layout';
import Auth from '@site/src/components/Authentication';
import { useHistory } from '@docusaurus/router';

export default function Developer() {
  const history = useHistory();

  const goToWiki = (project: string) => {
    history.push(`/docs/${project}/developer/features`);
  };

  return (
    <Layout title="Docs For Developer">
      <Auth>
        <div style={{ padding: '20px' }}>
          <h1>Developer Wiki</h1>
          <p>Chọn dự án để xem tài liệu phát triển</p>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: '20px', marginTop: '20px' }}>
            <div
              style={{ padding: '15px', border: '1px solid #ddd', borderRadius: '5px', cursor: 'pointer' }}
              onClick={() => goToWiki('datatp-crm')} >
              <h3>DataTP CRM</h3>
              <p>Tài liệu phát triển cho CRM</p>
            </div>

            <div style={{ padding: '15px', border: '1px solid #ddd', borderRadius: '5px', cursor: 'pointer' }}
              onClick={() => goToWiki('datatp-tms')} >
              <h3>DataTP TMS</h3>
              <p>Tài liệu phát triển cho TMS</p>
            </div>

            <div style={{ padding: '15px', border: '1px solid #ddd', borderRadius: '5px', cursor: 'pointer' }}
              onClick={() => goToWiki('document-ie')} >
              <h3>Document IE</h3>
              <p>Tài liệu phát triển cho Document IE</p>
            </div>
          </div>
        </div>
      </Auth>
    </Layout>
  );
}