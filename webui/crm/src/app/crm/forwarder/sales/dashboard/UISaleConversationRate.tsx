import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util, sql, entity, grid } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { T } from "../backend";

const SESSION = app.host.DATATP_HOST.session;

class SaleConversationRateTreePlugin extends grid.TreeDisplayModelPlugin {

  override setCollapse(record: grid.TreeRecord) {
    record.collapse = true;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let salemanLabelMap: Map<any, any> = new Map();
    records.forEach(record => {
      if (record.salemanAccountId && record.salemanLabel) {
        salemanLabelMap.set(record.salemanAccountId, record.salemanLabel);
      }
    });

    let treeRecords: Array<any> = [];
    let idCounter = 1;
    let salemanGroups: util.ListRecordMap<string> = new util.ListRecordMap<string>();
    salemanGroups.addAllRecords('salemanAccountId', records);

    for (let salemanAccountId of salemanGroups.getListNames()) {
      let records: Array<any> = salemanGroups.getList(salemanAccountId);
      let salemanNode: any = {
        id: idCounter++,
        groupType: 'Saleman',
        parentId: undefined,
        referenceEntityId: undefined,
        salemanAccountId: salemanAccountId,
        salemanLabel: salemanLabelMap.get(Number(salemanAccountId)) || 'N/A',
        createdDate: '-',
        meetingCount: '-',
        customerLeadStatus: '-'
      }
      treeRecords.push(salemanNode);

      records.forEach((record, _index, _records) => {
        const customerLeadNode = {
          id: idCounter++,
          groupType: 'CustomerLead',
          parentId: salemanNode.id,
          referenceEntityId: record.customerLeadId,
          customerLeadName: record.customerLeadName,
          createdDate: record.createdDate,
          meetingCount: record.meetingCount,
          customerLeadStatus: record.customerLeadStatus,
        }
        treeRecords.push(customerLeadNode);
      })
    }
    grid.initRecordStates(treeRecords);

    return super.buildTreeRecords(treeRecords);
  }
}

export interface SaleDailyTaskTreeProps extends entity.DbEntityListProps {
}

export class UISaleConversationRateTree extends entity.DbEntityList<SaleDailyTaskTreeProps> {

  createVGridConfig(): grid.VGridConfig {
    const { plugin, appContext, pageContext } = this.props;
    const CELL_HEIGHT: number = 40;

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: CELL_HEIGHT,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T(`Label`), width: 550, filterable: true,
            fieldDataGetter: (record: any) => {
              return record['salemanLabel'] || 'N/A';
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;

              let uiList = ctx.uiRoot as UISaleConversationRateTree;
              const { appContext, pageContext } = uiList.props;
              let type = record['groupType'];

              if (type === 'Saleman') {
                let employeeName: string = record['salemanLabel'] || 'N/A';
                let parts = employeeName.trim().toLowerCase().split(' ');
                parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));

                if (parts.length >= 3) {
                  let initCount = parts.length - 2; // Luôn giữ lại 2 chữ cuối
                  let initials = parts.slice(0, initCount).map(word => word[0] + '.').join('');
                  employeeName = `${initials} ${parts.slice(-2).join(' ')}`;
                }

                const onCollapseRecord = (dRecord: grid.DisplayRecord) => {
                  dRecord.model['collapse'] = !dRecord.model['collapse'];
                  let displayRecordList = ctx.model.getDisplayRecordList();
                  if (displayRecordList instanceof grid.TreeDisplayModel) {
                    displayRecordList.updateDisplayRecords();
                    ctx.getVGrid().forceUpdateView();
                  }
                }

                return (
                  <div className='flex-hbox justify-content-center align-items-center' onClick={() => onCollapseRecord(dRecord)}>
                    <module.account.WAvatars className='px-2'
                      appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                      avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                    <div className="flex-hbox"
                      style={{
                        cursor: 'pointer',
                        userSelect: 'text',
                        WebkitUserSelect: 'text',
                        MozUserSelect: 'text',
                        msUserSelect: 'text'
                      }}
                      onContextMenu={(e) => {
                        e.stopPropagation();
                        const range = document.createRange();
                        range.selectNodeContents(e.currentTarget);
                        const selection = window.getSelection();
                        selection?.removeAllRanges();
                        selection?.addRange(range);
                      }}
                    >
                      {employeeName}
                    </div>
                  </div>
                )
              }
              return (
                <div className='flex-hbox'>
                  {record['customerLeadName'] || 'N/A'}
                </div>
              );
            },
          },
          {
            name: 'createdDate', label: T('Created Date'), width: 140, filterable: true, filterableType: 'date',
            fieldDataGetter: (record: any) => {
              if (record['createdDate'] === '-') return record['createdDate'];
              return util.text.formater.compactDate(record['createdDate']);
            },
          },
          {
            name: 'meetingCount', label: T('Meeting Count'), width: 140,
            cssClass: 'flex-hbox justify-content-center align-items-center',
          },
          { name: 'customerLeadStatus', label: T('Status'), width: 130, filterable: true },
        ],
      },
      toolbar: {
        hide: true,
        actions: [
        ],
      },
      footer: {
      },
      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Tree View',
            treeField: 'label',
            plugin: new SaleConversationRateTreePlugin()
          }
        }
      },
    }
    return config;
  }

}



export interface UISaleConversationRateProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UISaleConversationRate extends app.AppComponent<UISaleConversationRateProps> {


  records: Array<any> = [];

  constructor(props: UISaleConversationRateProps) {
    super(props);
    this.loadData();
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { appContext } = this.props;

    let searchParams: sql.SqlSearchParams = {
      params: { companyId: 8 },
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
      ],
      maxReturn: 100000
    }

    this.markLoading(true);
    appContext.createHttpBackendCall('SaleTaskReportService', 'saleConversationRateReport', { params: searchParams })
      .withSuccessData((records: Array<any>) => {
        this.records = records;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  onViewAll = () => {

  }

  render(): React.ReactNode {

    const { appContext, pageContext } = this.props;
    return (
      <div className="flex-vbox p-2 mx-2 bg-white rounded-md w-100 h-100">

        <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
          <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Sale Conversation Rate from Visits</h5>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf="secondary" outline size="sm" className="me-1 p-1"
              onClick={() => { }}>
              <FeatherIcon.Download size={14} className="me-1" />
              Export
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="p-1"
              onClick={() => { }}>
              View all
              <FeatherIcon.ExternalLink size={12} className="ms-2" />
            </bs.Button>
          </div>
        </div>

        {
          this.records.length > 0 &&
          <div className="flex-vbox">
            <UISaleConversationRateTree appContext={appContext} pageContext={pageContext}
              plugin={new entity.DbEntityListPlugin(this.records)} />
          </div>
        }

      </div>
    );
  }
}