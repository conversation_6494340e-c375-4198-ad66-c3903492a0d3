import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util } from '@datatp-ui/lib';
import {
  UIPartnerEventHistoryReportList,
  UIPartnerEventHistoryReportPlugin
} from "../leads/UIPartnerEventHistoryReportList";
import { SaleDailyTaskTree } from "../report/SaleDailyTaskTreeList";
import { UISaleDailyTaskListPlugin } from "../report/UISaleDailyTaskList";
import { UIInquiryRequestList, UIInquiryRequestReportPlugin } from "../../price";
import {
  UISpecificQuotationList,
  UISpecificQuotationPlugin
} from "../quotation/specific/UISpecificQuotationList";
import { UIBookingList, UIBookingListPlugin } from "../booking/UIBookingList";
import { UISaleReport, UISaleReportPlugin } from "./UISaleReport";
import { WSaleActivitiesReport } from "./UIRecentActivitiesReport";
import { UISalemanSystemPerformance } from "./UISalemanSystemPerformance";
import { UISaleConversationRate } from "./UISaleConversationRate";

export interface UISaleDashboardProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
interface UISaleDashboardState {
  showButtons: boolean;
}
export class UISaleDashboard extends app.AppComponent<UISaleDashboardProps, UISaleDashboardState> {

  constructor(props: UISaleDashboardProps) {
    super(props);

    this.state = {
      showButtons: false,
    }

  }

  handlePartnerLostClick = () => {
    // Handle partner lost action
    console.log('Partner Lost clicked');
  }

  handleCustomerLeadClick = () => {
    // Handle customer lead due date action
    console.log('Customer Lead Due Date clicked');
  }

  onMouseEnterCard = (e: any) => {
    e.target.style.backgroundColor = '#e9ecef';
  }

  onMouseLeaveCard = (e: any) => {
    e.target.style.backgroundColor = '#f8f9fa';
  }

  onShowPartnerDailyTasks = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIPartnerEventHistoryReportList appContext={appCtx} pageContext={pageCtx}
          plugin={new UIPartnerEventHistoryReportPlugin()} />
      );
    }
    let pupupLabel: string = `Leads/ Customers Recent Activities`;
    pageContext.createPopupPage('partner-daily-tasks', pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllDailyTasks = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UISaleDailyTaskListPlugin('Company', new Date());
      return (
        <SaleDailyTaskTree appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      );
    }
    let popupId = `view-all-tasks-${util.IDTracker.next()}`;
    let pupupLabel: string = `Sales Daily Activities`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllInquiryRequests = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIInquiryRequestReportPlugin('Company');
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space="Company" />
      );
    }
    let popupId = `view-all-inquiry-requests-${util.IDTracker.next()}`;
    let pupupLabel: string = `Inquiry Requests`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllQuotations = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UISpecificQuotationPlugin('Company');
      return (
        <UISpecificQuotationList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space="Company" />
      );
    }
    let popupId = `view-all-quotations-${util.IDTracker.next()}`;
    let pupupLabel: string = `Quotations`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllBookings = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIBookingListPlugin('Company');
      return (
        <UIBookingList appContext={appCtx} pageContext={pageCtx} plugin={plugin} type="page" />
      );
    }
    let popupId = `view-all-bookings-${util.IDTracker.next()}`;
    let pupupLabel: string = `Bookings`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllSaleUserAnalysis = () => {
    let { pageContext, space } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIBookingListPlugin('Company');
      return (
        <UISaleReport appContext={appCtx} pageContext={pageCtx} plugin={new UISaleReportPlugin(space)} />
      );
    }
    let popupId = `view-all-sale-user-analysis-${util.IDTracker.next()}`;
    let pupupLabel: string = `User Analysis`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  renderDailyTasksActivities() {
    const hoverStyle = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)'
    };

    const textColor = '#1A5319';
    const backgroundColor = '#D6EFD8';
    const gradientBackground = 'linear-gradient(135deg, #D6EFD8, #80AF81)';
    const buttonHoverColor = '#fff';

    return (
      <div className="flex-hbox justify-content-center align-items-center my-2 px-2">
        <div className="flex-hbox justify-content-center align-items-center rounded-md w-100"
          style={{
            color: textColor,
            padding: '18px 12px',
            minHeight: '100px',
            maxHeight: '100px',
            transition: 'all 0.3s ease',
            backgroundColor: backgroundColor,
            position: 'relative',
            border: '1px solid #80AF81',
          }}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
            this.setState({ showButtons: true });
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            this.setState({ showButtons: false });
          }} >

          {!this.state.showButtons && (
            <div className="d-flex align-items-center" style={{ color: textColor }}>
              <FeatherIcon.CheckSquare size={24} className="me-3" style={{ color: '#508D4E' }} />
              <h3 className="fw-bold" style={{ color: textColor, margin: 0 }}>Daily Tasks Activities</h3>
            </div>
          )}

          {this.state.showButtons && (
            <div className="d-flex w-100 justify-content-between gap-4" >
              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onClick={this.onViewAllDailyTasks}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.List size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>All Tasks</h5>
              </div>

              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onClick={this.onShowPartnerDailyTasks}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.Users size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>Leads/ Customers</h5>
              </div>

              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onClick={this.onViewAllSaleUserAnalysis}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.Users size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>Users Monitoring</h5>
              </div>

              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.Download size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>Export Excel</h5>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Render method for Partner Lost section
  renderPartnerLostSection() {
    // Color palette
    const primaryColor = '#1A5319';
    const secondaryColor = '#508D4E';
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const cardStyle: any = {
      border: `1px solid ${borderColor}`,
      borderRadius: '8px',
      padding: '20px',
      margin: '8px',
      backgroundColor: '#fff',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      position: 'relative',
      minHeight: '120px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    };

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)'
    };

    const iconStyle: any = {
      marginBottom: '12px',
      color: secondaryColor
    };

    const titleStyle: any = {
      fontSize: '16px',
      fontWeight: '600',
      color: primaryColor,
      marginBottom: '8px'
    };

    const actionButtonsStyle: any = {
      position: 'absolute',
      top: '12px',
      right: '12px',
      display: 'flex',
      gap: '6px',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    };

    const actionButtonStyle: any = {
      backgroundColor: lightColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '12px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      color: primaryColor
    };

    return (
      <div style={cardStyle} className="flex-grow-1"
        onClick={this.handlePartnerLostClick}
        onMouseEnter={(e: any) => {
          Object.assign(e.currentTarget.style, hoverStyle);
          e.currentTarget.querySelector('.action-buttons').style.opacity = '1';
        }}
        onMouseLeave={(e: any) => {
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.querySelector('.action-buttons').style.opacity = '0';
        }} >
        <div style={iconStyle}>
          <FeatherIcon.UserX size={24} />
        </div>
        <div style={titleStyle}>Partner Lost</div>
        <div style={{ fontSize: '14px', color: secondaryColor }}>
          Track and analyze lost partnerships
        </div>

        {/* Action Buttons */}
        <div className="action-buttons" style={actionButtonsStyle as React.CSSProperties}>
          <button style={actionButtonStyle}
            onClick={(e: any) => {
              e.stopPropagation();
              console.log('View details clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Eye size={12} style={{ marginRight: '4px' }} />
            View
          </button>
          <button
            style={actionButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('Edit clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Edit3 size={12} style={{ marginRight: '4px' }} />
            Edit
          </button>
        </div>
      </div>
    );
  }

  renderCustomerLeadDueDateSection() {
    // Color palette
    const primaryColor = '#1A5319';
    const secondaryColor = '#508D4E';
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const cardStyle: any = {
      border: `1px solid ${borderColor}`,
      borderRadius: '8px',
      padding: '20px',
      margin: '8px',
      backgroundColor: '#fff',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      position: 'relative',
      minHeight: '120px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    };

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)'
    };

    const iconStyle: any = {
      marginBottom: '12px',
      color: secondaryColor
    };

    const titleStyle: any = {
      fontSize: '16px',
      fontWeight: '600',
      color: primaryColor,
      marginBottom: '8px'
    };

    const actionButtonsStyle: any = {
      position: 'absolute',
      top: '12px',
      right: '12px',
      display: 'flex',
      gap: '6px',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    };

    const actionButtonStyle: any = {
      backgroundColor: lightColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '12px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      color: primaryColor
    };

    return (
      <div style={cardStyle} className="flex-grow-1"
        onClick={this.handleCustomerLeadClick}
        onMouseEnter={(e: any) => {
          Object.assign(e.currentTarget.style, hoverStyle);
          e.currentTarget.querySelector('.action-buttons').style.opacity = '1';
        }}
        onMouseLeave={(e: any) => {
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.querySelector('.action-buttons').style.opacity = '0';
        }} >
        <div style={iconStyle}>
          <FeatherIcon.Calendar size={24} />
        </div>
        <div style={titleStyle}>Customer Lead Due Date</div>
        <div style={{ fontSize: '14px', color: secondaryColor }}>
          Monitor upcoming lead deadlines
        </div>

        {/* Action Buttons */}
        <div className="action-buttons" style={actionButtonsStyle as React.CSSProperties}>
          <button style={actionButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('View calendar clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Calendar size={12} style={{ marginRight: '4px' }} />
            Calendar
          </button>
          <button style={actionButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('Set reminder clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Bell size={12} style={{ marginRight: '4px' }} />
            Remind
          </button>
        </div>
      </div>
    );
  }

  // Render method for Inquiry, Quotation, Booking section
  renderInquiryQuotationBookingSection() {
    // Color palette
    const primaryColor = '#1A5319';
    const secondaryColor = '#508D4E';
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const cardStyle = {
      minHeight: '200px',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      backgroundColor: '#fff',
      border: `1px solid ${borderColor}`,
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    };

    const hoverStyle = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    const iconStyle = {
      color: secondaryColor
    };

    const titleStyle = {
      color: primaryColor,
      margin: 0,
      fontWeight: '600'
    };

    return (
      <div className="mb-3 px-2 flex-hbox gap-3">
        <div className="flex-grow-1 rounded-md" style={cardStyle}
          onClick={this.onViewAllInquiryRequests}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="d-flex flex-column align-items-center">
            <FeatherIcon.HelpCircle size={32} className="mb-3" style={iconStyle} />
            <h3 className="fw-bold" style={titleStyle}>Inquiry Requests</h3>
            <span style={{ fontSize: '14px', color: secondaryColor, marginTop: '8px' }}>
              Manage customer inquiries
            </span>
          </div>
        </div>

        <div className="flex-grow-1 rounded-md"
          style={cardStyle}
          onClick={this.onViewAllQuotations}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="d-flex flex-column align-items-center">
            <FeatherIcon.FileText size={32} className="mb-3" style={iconStyle} />
            <h3 className="fw-bold" style={titleStyle}>Quotations</h3>
            <span style={{ fontSize: '14px', color: secondaryColor, marginTop: '8px' }}>
              View and create quotations
            </span>
          </div>
        </div>

        <div className="flex-grow-1 rounded-md" style={cardStyle}
          onClick={this.onViewAllBookings}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="d-flex flex-column align-items-center">
            <FeatherIcon.Calendar size={32} className="mb-3" style={iconStyle} />
            <h3 className="fw-bold" style={titleStyle}>Bookings</h3>
            <span style={{ fontSize: '14px', color: secondaryColor, marginTop: '8px' }}>
              Manage internal bookings
            </span>
          </div>
        </div>
      </div>
    );
  }

  render() {
    if (this.isLoading()) return this.renderLoading();

    return (
      <div className="flex-vbox mx-1">
        <bs.GreedyScrollable className="my-1">

          {this.renderDailyTasksActivities()}

          <UISalemanSystemPerformance {...this.props} />

          <div className="flex-hbox mb-3">
            {this.renderPartnerLostSection()}
            {this.renderCustomerLeadDueDateSection()}
          </div>

          <div className="flex-hbox mb-3 gap-2" style={{ maxHeight: 400, minHeight: 400 }}>
            <div className="flex-grow-0" style={{ width: 500 }}>
              <WSaleActivitiesReport {...this.props} />
            </div>
            <div className="flex-grow-1">
              <UISaleConversationRate {...this.props} />
            </div>
          </div>

          {this.renderInquiryQuotationBookingSection()}

        </bs.GreedyScrollable>
      </div>
    );
  }
}