import { themes as prismThemes } from 'prism-react-renderer';
import type { Config } from '@docusaurus/types';
import type * as Preset from '@docusaurus/preset-classic';

const config: Config = {
  title: 'DataTP Cloud Documentation',
  tagline: 'Documentation for DataTP Projects',
  favicon: 'img/favicon.ico',

  // Set the production url of your site here
  url: 'https://docs.beelogistics.cloud/',
  // Set the /<baseUrl>/ pathname under which your site is served
  // For GitHub pages deployment, it is often '/<projectName>/'
  baseUrl: '/',

  // GitHub pages deployment config.
  // If you aren't using GitHub pages, you don't need these.
  projectName: 'beelogistics-docs',

  onBrokenLinks: 'throw',
  onBrokenMarkdownLinks: 'warn',

  // scripts: [
  //   {
  //     src: '/js/redirect.js',
  //     async: true,
  //   },
  // ],

  // Even if you don't use internationalization, you can use this field to set
  // useful metadata like html lang. For example, if your site is Chinese, you
  // may want to replace "en" with "zh-<PERSON>".
  i18n: {
    defaultLocale: 'vi', // Cập nhật ngôn ngữ mặc định thành tiếng Việt
    locales: ['vi', 'en'],
    path: 'i18n',
    localeConfigs: {
      vi: {
        htmlLang: 'vi-VN', // Cấu hình ngôn ngữ HTML cho tiếng Việt
        direction: 'ltr',
      },
      en: {
        htmlLang: 'en-GB',
        direction: 'ltr',
      },
    }
  },

  presets: [
    [
      'classic',
      {
        docs: {
          sidebarPath: require.resolve('./sidebars.js'),
          include: [
            '**/user/**/*.{md,mdx}',
            '**/shared/**/*.{md,mdx}',
            '**/developer/**/*.{md,mdx}',
          ],
          exclude: [
            '**/@*.{md,mdx}',
          ],
          routeBasePath: '/docs',
        },
        theme: {
          customCss: './src/css/custom.css',
        },
      } satisfies Preset.Options,
    ],
  ],

  themeConfig: {
    // Replace with your project's social card
    // image: 'img/banner_web.png',
    navbar: {
      logo: {
        alt: 'DataTP Cloud Logo',
        src: 'img/logo.png',
        href: '/',
      },
      items: [
        { to: '/docs/shared/user/system', label: 'User Guides', position: 'left' },
        { to: '/docs/shared/developer/SETUP', label: 'Developer', position: 'left' },
        {
          type: 'localeDropdown',
          position: 'right',
        },
      ],
    },
    footer: {
      style: 'dark',
      copyright: `Copyright © ${new Date().getFullYear()} DataTP Cloud.`,
    },
    prism: {
      theme: prismThemes.github,
      darkTheme: prismThemes.dracula,
    },
    docs: {
      sidebar: {
        hideable: true,
        autoCollapseCategories: true,
      },
    },
    tableOfContents: {
      minHeadingLevel: 2,
      maxHeadingLevel: 4,
    },
  } satisfies Preset.ThemeConfig,
};

export default config;
