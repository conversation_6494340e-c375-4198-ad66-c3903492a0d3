{"theme.ErrorPageContent.title": {"message": "<PERSON><PERSON> này đã bị lỗi.", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "Trở lại đầu trang", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "<PERSON><PERSON><PERSON> tr<PERSON>", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "<PERSON><PERSON><PERSON> tr<PERSON>", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "<PERSON><PERSON> đi<PERSON>u hướng của trang danh sách bài viết", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "<PERSON><PERSON><PERSON> bài mới hơn", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> c<PERSON> h<PERSON>n", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "<PERSON><PERSON> đi<PERSON>u hướng của trang bài viết", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "<PERSON><PERSON><PERSON> mới h<PERSON>n", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "<PERSON><PERSON><PERSON> c<PERSON> h<PERSON>n", "description": "The blog post button label to navigate to the older/next post"}, "theme.tags.tagsPageLink": {"message": "<PERSON><PERSON> tất cả Thẻ", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "<PERSON>y<PERSON>n đổi chế độ sáng và tối (hiện tại {mode})", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "ch<PERSON> độ tối", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "<PERSON><PERSON> độ sáng", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "<PERSON><PERSON><PERSON> kết đi<PERSON> h<PERSON>", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "{count} mục", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "{count} tài liệu đã gắn thẻ", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged} với \"{tagName}\"", "description": "The title of the page for a docs tag"}, "theme.docs.paginator.navAriaLabel": {"message": "<PERSON><PERSON> tài li<PERSON>u", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "<PERSON><PERSON> tiếp", "description": "The label used to navigate to the next doc"}, "theme.docs.versionBadge.label": {"message": "<PERSON><PERSON><PERSON> b<PERSON>: {<PERSON><PERSON><PERSON><PERSON>}"}, "theme.common.editThisPage": {"message": "<PERSON><PERSON><PERSON> trang n<PERSON>y", "description": "The link label to edit the current page"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "<PERSON><PERSON><PERSON> là tài liệu chưa đ<PERSON><PERSON><PERSON> phát hành chính thức của {siteTitle} phi<PERSON><PERSON> bản {versionLabel}.", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "<PERSON><PERSON><PERSON> là tài liệu của {siteTitle} {versionLabel}, hiện không còn đư<PERSON><PERSON> bảo trì.", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "<PERSON><PERSON> xem các cập nhật mới nhất, vui lòng xem phiên bản {latestVersionLink} ({versionLabel}).", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> bản mới nhất", "description": "The label used for the latest version suggestion link label"}, "theme.common.headingLinkTitle": {"message": "Đ<PERSON>ờng dẫn trực tiếp tới {heading}", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": " vào {date}", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": " bởi {user}", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "<PERSON><PERSON><PERSON> nh<PERSON>t lần cu<PERSON>{atDate}{byUser}", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.NotFound.title": {"message": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "description": "The title of the 404 page"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "<PERSON><PERSON><PERSON>", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.tags.tagsListLabel": {"message": "Thẻ:", "description": "The label alongside a tag list"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "Đ<PERSON><PERSON>", "description": "The ARIA label for close button of announcement bar"}, "theme.admonition.caution": {"message": "cẩn thận", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "c<PERSON><PERSON> b<PERSON>o", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "thông tin", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "ghi chú", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "mẹo", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "warning", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.blog.sidebar.navAriaLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> hướ<PERSON> các bài viết gần đây trên blog", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "Đã sao chép", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "Sao chép code vào bộ nhớ tạm", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "Sao chép", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "Chuyển đổi văn bản xuống dòng", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "<PERSON>em thêm danh mục '{label}'", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "<PERSON>hu gọn danh mục '{label}'", "description": "The ARIA label to collapse the sidebar category"}, "theme.NavBar.navAriaLabel": {"message": "<PERSON><PERSON> đ<PERSON><PERSON> h<PERSON>", "description": "The ARIA label for the main navigation"}, "theme.NotFound.p1": {"message": "<PERSON><PERSON>g tôi không thể tìm thấy những gì bạn đang tìm kiếm.", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "<PERSON><PERSON> lòng liên hệ với trang web đã dẫn bạn tới đây và thông báo cho họ biết rằng đường dẫn này bị hỏng.", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "description": "The label used by the button on the collapsible TOC component"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "<PERSON><PERSON><PERSON>", "description": "The label for the mobile language switcher dropdown"}, "theme.blog.post.readMore": {"message": "<PERSON><PERSON><PERSON>", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "<PERSON><PERSON><PERSON> thêm về {title}", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "{readingTime} phút để đọc", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "Trang chủ", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "<PERSON><PERSON> gọn thanh bên", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "<PERSON><PERSON> gọn thanh bên", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.navAriaLabel": {"message": "<PERSON><PERSON> đi<PERSON>u hướng tài liệu", "description": "The ARIA label for the sidebar navigation"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← Trở lại menu chính", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "Đóng - mở thanh điều hướng", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.docs.sidebar.expandButtonTitle": {"message": "Mở rộng thanh bên", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "Mở rộng thanh bên", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "<PERSON><PERSON><PERSON>h điều hư<PERSON>", "description": "The ARIA label for close button of mobile sidebar"}, "theme.blog.post.plurals": {"message": "{count} b<PERSON><PERSON> vi<PERSON><PERSON>", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} <PERSON><PERSON><PERSON><PERSON> g<PERSON> thẻ \"{tagName}\"", "description": "The title of the page for a blog tag"}, "theme.blog.author.pageTitle": {"message": "{authorName} - {nPosts}", "description": "The title of the page for a blog author"}, "theme.blog.authorsList.pageTitle": {"message": "Authors", "description": "The title of the authors page"}, "theme.blog.authorsList.viewAll": {"message": "View All Authors", "description": "The label of the link targeting the blog authors page"}, "theme.contentVisibility.unlistedBanner.title": {"message": "<PERSON>rang không công khai", "description": "The unlisted content banner title"}, "theme.contentVisibility.unlistedBanner.message": {"message": "Trang này không được công khai. Công cụ tìm kiếm sẽ không đánh chỉ mục cho trang này và chỉ những người có liên kết mới có thể truy cập được trang.", "description": "The unlisted content banner message"}, "theme.contentVisibility.draftBanner.title": {"message": "Draft page", "description": "The draft content banner title"}, "theme.contentVisibility.draftBanner.message": {"message": "This page is a draft. It will only be visible in dev and be excluded from the production build.", "description": "The draft content banner message"}, "theme.ErrorPageContent.tryAgain": {"message": "<PERSON><PERSON><PERSON> lại", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "<PERSON><PERSON><PERSON><PERSON> tới nội dung", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "Thẻ", "description": "The title of the tag list page"}}