---
sidebar_position: 2
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# Changelog

All notable changes to this project will be documented in this file.

### [R20250621]

Cập nh<PERSON><PERSON> nh<PERSON>h develop

Cập nhật dự án datatp-crm:
- Checkout code (nh<PERSON>h develop) và setup:  git@gitlab:datatp.net:tuan/datatp-crm.git
- Remove node_modules pnpm-lock.yaml dist và pnpm install && pnpm run build ở các dự án lib, erp, document-ie, logistics, crm, phoenix.
- Build java code bằng command: ./datatp.sh lgc:build -clean -build
- Run code như bình thường.

C<PERSON> thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250621]

Cập nhật nhánh document-ie:
- Em clear bảng nằm trên database cũ và fix bug bên doument set ạ

Cập nhật nhánh tms:
[Quân] :
1. <PERSON> phép cus tự lên thông tin xe thay điều vận:
- Gia<PERSON> diện TMSBillList hiển thị popup các tab VehicleTrip Form cho phép nhập thông tin lô hàng
2. Tự động ghép chuyến cho các lô hàng theo request của cus:
- Giao diện: Thêm 1 checkbox Combine Trip tại popup Processing Goods Request
- Backend: Tạo VehicleTrip chung cho các VehicleTripGoodsTracking được tạo từ các TMSBill request.
3. Export template xuất thông tin xe cho BFSOne theo feedback của người dùng.
Thêm note cảnh báo các lô chưa đủ điều kiện thanh toán(Sai hbl, chưa nhập giá, sai đơn vị)

Chiến:
+ Dựng Entity TMSHouseBill: Số Hbl, Loại Hình(Nhập/Xuất), Customer, Kho bãi lấy/trả hàng, Carrier, COT/ETA,...
+ Thêm các hàm logic sử lý CRUD. Tối ưu hàm search khi join bảng tms-bill
+ Migration TMS HouseBill data, tạo liên kết đến TMSBill tương ứng
+ Dựng màn hình cho TMSHouseBillList và TMSHouseBillEditor

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- migrate:run --script document/CleanDocument.groovy
- server:migrate:run --script tms/MigrationTMSHouseBill.groovy

### [R20250620]

Cập nhật nhánh document-ie:
Em fix bug search document set ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [*********]

Cập nhật nhánh crm:
[Dan] - Implement UI Dashboard Salesman Activity Tracker.

Cập nhật dự án document-ie:
- Checkout code (nhánh develop):  git@gitlab:datatp.cloud:tuan/datatp-document-ie.git
- Tạo db, user và restore theo link db tương ứng ở dưới. (Sửa thông tin ở file env.sh, chạy lại các lệnh ở file postgres-admin.sh)
db_name: document_ie_db
username: document_ie
password: document_ie

Xem lại cấu hình các file config application.properties, bổ sung thêm datasource cho db mới.
document-ie:
type: com.zaxxer.hikari.HikariDataSource
connectionTimeout: 30000
idleTimeout: 600000
maxLifetime: 600000
minimumIdle: 5
maximumPoolSize: 15
auto-commit: false
driverClassName: `***********************************************:{spring.datasource.server.port}/document_ie_db`
username: document_ie
password: document_ie

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
Có thể download db document_ie_db mới tại đường dẫn https://beelogistics.cloud/download/document_ie_db-latest.dump

### [*********]

Cập nhật nhánh crm:
- [Dan] - Implement UI Dashboard cho Pricing Company.

[Nhat]
- Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail
- Check Customer theo Tax Code: Bổ sung Button Check, Check thêm Customer Lead
- Thêm button tạo Shipping Instruction từ Partner Obligation, search Partner Obligation theo Cus/ Docs

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [*********]

Cập nhật nhánh quan-tms:
- Thêm nút clear trạng thái thanh toán với quyền moderator tại màn hình TMSBillList
- Điều chỉnh hiển thị các điểm dừng tại màn TMSBillList, VehicleTripGoodTrackingList theo yêu cầu

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250616]

Cập nhật nhánh crm:
- [An] - Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)
- [An] -Refactor lại thông tin BFSOne Partner, Viết cron sync tự động theo ngày
- [An] - "Cập nhật bảng giá cont (Trucking): Chia lại mức giá cho cont 20' theo y/c của HPH.

- [Nhat] - Clean code Uncompleted Sale Daily Task, thay thế logic sendMessage cũ bằng CRMMessageSystem
- [Nhat] - Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail

- [Dan] - Implement UI Dashboard cho CRM Company.
- [Dan] - Viết api gen token, api cập nhật BFSOne Partner Code cho a Quý.
   Quy trình lúc tạo mới partner -> KT approve -> BFSOne gen code partner -> Gọi api để sync lại với DataTP
   Đã gửi lại api cho a Quý lúc  13/06
   https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing"
- [Dan] - Tạo bảng dữ liệu, lưu thông tin khách hàng
   Tạo các api service cho phép cập nhật, chỉnh sửa và xoá record.
- [Dan] - "Cập nhật api liên quan đến authorization, cho phép các hệ thống bên ngoài gọi vào các service để cập nhật dữ liệu.
   Hard code token, gửi cho client sau đó kiểm tra request để xác thực."
- [Dan] - Tạo bảng dữ liệu, lưu thông tin unit
   Tạo các api service cho phép sync, cập nhật, chỉnh sửa và xoá record.

Cập nhật nhánh quan-tms:
- Giao diện TMSBillList, VehicleTripGoodTrackingList cho phép nhập thêm các điểm dừng
- Hàm search TMSBillList, VehicleTripGoodTrackingList load thêm các điểm dừng

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

### [R20250612]

Cập nhật nhánh crm:
- Refactor lại thông tin BFSOne Partner
- Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)

Cập nhật nhánh tms:
- [Quân] Drop bảng document_document_set_category, drop column document_category_id tại bảng document_document_set,  drop các index đặt tên sai tại bảng document_document
- [Chiến] Fix bugs TMSBillFee save lỗi, Clear thông tin xe và giá cost khi copy lô hàng

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

- migrate:run --script document/CleanDocument.groovy

### [R20250612]

Cập nhật nhánh crm:
- Refactor lại thông tin BFSOne Partner
- Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)

Cập nhật nhánh tms:
- Địa chỉ config cho khách hàng thêm StreetName để lưu số nhà, đường,...(Kế toán yêu cầu tách)
 - Thêm senderStreetName, receiverStreetName trên TMSBill và đồng bộ với địa chỉ khách hàng đã config
- Fix BFSOne template export, chuẩn hóa địa chỉ  xã/phường, quận/huyên theo yêu cầu
- Fix lỗi query liên quan đến migration TMSVendor sang Vehicle Fleet (Quân chưa thay hết)

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250612]

Cập nhật nhánh crm:
- Clean code groovy sql không sử dụng từ datatp-build/app/cli
- Drop các colume thừa, tạo nhầm, không còn sử dụng ở các bảng bfsone_partner, lgc_price_bulk_cargo_inquiry_request, lgc_price_truck_container_charge

Cập nhật nhánh nhat:
- Chỉnh sửa giao diện Asset Calendar+ fix bug màn hình tạo Task
- Bổ sung màn hình Admin KPI

Cập nhật nhánh tms:
Task:
Chiến:
- Push cước từ Vehicle Goods Tracking về phần Chi phí TMSBill
- Cho phép cus đồng bộ cước khi Vehicle Goods Tracking đã được nhập cước
- Thêm trạng thái thông báo thanh toán chi phí TMSBill và các lỗi dẫn đến chưa đc thanh toán
- UITMSBillList lọc các bill chưa thanh toán
- Verify HouseBill tmsBill với BFSOne, cảnh báo các lô hàng HouseBill chưa Verify

Quân:
- [Vehicle Fleet] Thêm field emails và các field webhook config
- Viết groovy script merge tms vendor vào vehicle fleet và migration dữ liệu các entity dùng TMSVendor sang Vehicle Fleets
- Thay thế trên các giao diện màn hình dùng BBRefTMSVendor sang BBRefVehicleFleet.
- Chuyển và kiểm tra các chức năng call webhook được cấu hình từ TMS Partner sang Vehicle Fleet

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- migrate:run --script crm/AlterTables.groovy
- migrate:run --script tms/MigrationTmsBillFee.groovy
- server:migrate:run --script tms/MigrationVehicleFleet.groovy

### [R20250611]

Cập nhật nhánh crm:
- Sửa logo báo cáo

Cập nhật nhánh asset:
Task:
- [Asset] Thêm giao diện Calendar riêng cho book xe + phòng họp (trong module Asset). Default xem ở dạng week
- [Spreadsheet] Tạo bảng config màn hình Report team IST (BD HCM)

Cập nhật nhánh ocr:
Em fix bug trích rút bên kế toán ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

### [R20250610]

Cập nhật nhánh crm:

- Cập nhật form, feedback chỉnh sửa, bugs cho inquiry hàng rời (Team BD)
- Enhance các báo cáo ở màn hình CRM. (Lọc, filter, xuất excel, ..)
   Báo cáo hoạt động khách hàng/ lead theo dõi gần đây
- Fix bugs lỗi spam mail nhắc cập nhật request
- Cập nhật response MSA: String => MapObject

Cập nhật nhánh tms:

- Fix bugs liên quan đến GPS
- Fix bugs TMS liên quan đến TMSPartner và TMSBillFee

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

# Cập nhật server , code nhánh develop và download db

Cập nhật nhánh ocr:
- Bổ sung trích rút tên người bán người mua bên python và thêm vào bên giao diện
- cập nhật trích rút bên dat-ocr, code datatp-python

Cập nhật nhánh maintenance
- Remove react-beautiful-dnd thư viện
- Cập nhật kanban sử dụng dndkit lib

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

Nếu không cập nhật db, chạy script:
- migrate:run --script tms/MigrationTmsPartnerAddess.groovy
