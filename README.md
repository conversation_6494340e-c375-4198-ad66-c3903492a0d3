# DataTP Docs

Hệ thống tài liệu tập trung cho các microservices c<PERSON>a DataTP, bao gồm `datatp-crm`, `datatp-tms`, `document-ie`, ...

### Installation
1. Clone repository:
   ```bash
   git clone git@gitlab:datatp.net:tuan/datatp-docs.git
   cd datatp-docs
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```
3. Start the development server:
   ```bash
   pnpm run start
   ```
   Open `http://localhost:3000` to view the site.

### Build for Production
```bash
pnpm run build
```
The static site will be generated in the `build/` directory.

## Project Structure

The project follows a standardized folder structure for each microservice:

```
datatp-docs/
├── docs/
│   ├── datatp-crm/
│   │   ├── guides/         # Public user guides
│   │   ├── wiki/           # Private developer setup and tasks
│   │   ├── CHANGELOG.md    # Private version history
│   │   ├── BACKLOG.md      # Task tracking
│   ├── datatp-tms/
│   │   ├── guides/
│   │   ├── wiki/
│   │   ├── CHANGELOG.md
│   │   ├── BACKLOG.md
│   ├── datatp-document-ie/
│   │   ├── guides/
│   │   ├── wiki/
│   │   ├── CHANGELOG.md
│   │   ├── BACKLOG.md
│   ├── shared/             # Shared docs or other docs
├── static/
│   ├── img/                # Images and assets
├── src/
│   ├── components/         # Custom React components (e.g., Auth)
│   ├── css/                # Custom styles
│   ├── pages/              # Custom pages
├── docusaurus.config.js    # Docusaurus configuration
├── sidebars.js             # Sidebar navigation
├── package.json            # Dependencies
├── merge_docs.py           # Script to merge docs
└── README.md               # This file
```

### Documentation Types
- **Guides**: Public, built and deployed to the web for end-users.
- **Wiki**: Private, for developers.
- **Changelog**: Private, version history.
- **Backlog**: Private, Task Tracking.

## Contributing

### Adding Documentation
1. Navigate to the relevant project folder (e.g., `docs/datatp-crm/`).
2. Add Markdown files to the appropriate subfolder:
   - User guides: `guides/`
   - Developer wiki: `wiki/`
   - Changelogs: `CHANGELOG.md`
3. Follow the Markdown template (`docs/template.md`):
   ```markdown
   ---
    sidebar_position: 1 (order page)
    sidebar: false
    hide_table_of_contents: true/ false
    displayed_sidebar: namedSidebar
   ---

   # Document Title
   Describe the purpose of this document.

   #### 1. Title 1**
   - Item 1
   - Item 2

   #### 2. Title 1**
   - Item 1
   - Item 2
   ```

4. Update `sidebars.js` to include new documents in the navigation.

### Merging Documentation
To merge docs from multiple projects:
```bash
python merge_docs.py
```
This script copies Markdown files from `docs/{project}` to `docs/merged`.