import type { SidebarsConfig } from '@docusaurus/plugin-content-docs';
const sidebars: SidebarsConfig = {
  userSidebar: [
    {
      type: 'link',
      label: 'System',
      href: '/docs/shared/user/system',
    },
    {
      type: 'link',
      label: 'Car/Meeting Schedule',
      href: '/docs/shared/user/asset',
    },
    {
      type: 'category',
      label: 'OKR/ KPI',
      items: [
        {
          type: 'autogenerated',
          dirName: 'shared/user/okr',
        }
      ]
    },
    {
      type: 'category',
      label: 'Document IE',
      items: [
        {
          type: 'autogenerated',
          dirName: 'document-ie/user',
        }
      ]
    },
    {
      type: 'category',
      label: 'CRM',
      items: [
        {
          type: 'link',
          label: 'Overview',
          href: '/docs/datatp-crm/user/intro',
        },
        {
          type: 'link',
          label: 'Pricing Tools',
          href: '/docs/datatp-crm/user/pricing-tools',
        },
        {
          type: 'autogenerated',
          dirName: 'datatp-crm/user/crm',
        }
      ]
    },
  ],

  developerSidebar: [
    {
      type: 'link',
      label: 'Setup',
      href: '/docs/shared/developer/SETUP',
    },
    {
      type: 'link',
      label: 'Changelog',
      href: '/docs/shared/developer/CHANGELOG',
    },
    {
      type: 'link',
      label: 'Backlog',
      href: '/docs/shared/developer/BACKLOG',
    },
    {
      type: 'category',
      label: 'CRM',
      items: [
        {
          type: 'autogenerated',
          dirName: 'datatp-crm/developer',
        }
      ]
    },
    {
      type: 'category',
      label: 'Document IE',
      items: [
        {
          type: 'autogenerated',
          dirName: 'document-ie/developer',
        }
      ]
    },
  ],

};
export default sidebars;
