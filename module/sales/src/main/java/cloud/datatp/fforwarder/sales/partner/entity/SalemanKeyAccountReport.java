package cloud.datatp.fforwarder.sales.partner.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 */
@Slf4j
@Entity
@Table(name = SalemanKeyAccountReport.TABLE_NAME, uniqueConstraints = {
    @UniqueConstraint(name = SalemanKeyAccountReport.TABLE_NAME + "_code", columnNames = { "code" }),
}, indexes = {})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class SalemanKeyAccountReport extends CompanyEntity {

  private static final long serialVersionUID = 1L;

  final static public String TABLE_NAME = "forwarder_saleman_key_account_report";

  public static enum ReportType { SALES, BD }

  @NotNull
  private String code;

  @Enumerated(EnumType.STRING)
  private ReportType type = ReportType.SALES;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "submitted_date")
  private Date submittedDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "reported_date_from")
  private Date reportedDateFrom;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "reported_date_to")
  private Date reportedDateTo;

  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  private double profit = 0;

  private double volume = 0;

  private double revenue = 0;

  @Transient
  private MapObject volumePerformance = new MapObject();

  /*
   * Key Account Report
   * forecast: { forecast: '' }
   *
   * BD Key Account Report
   * highlights : { signedAaContracts: '', seaVolume: '', estimate: '' }
   */
  // HIGHLIGHTS
  @Transient
  private MapObject highlights = new MapObject();

  /*
   * Key Account Report
   * forecast: { forecast: '' }
   *
   * BD Key Account Report
   * forecast: { airVolume: '', seaVolume: '', estimate: '' }
   */
  // FORECAST
  @Transient
  private MapObject forecast = new MapObject();

  // SUGGESTION/REQUEST
  @Column(name = "suggestion_or_request", length = 2 * 1024)
  private String suggestionOrRequest;

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.LAZY)
  @Column(name = "volume_performance", length = 64 * 1024)
  public String getVolumePerformanceJson() {
    if (this.volumePerformance == null)
      return null;
    return DataSerializer.JSON.toString(this.volumePerformance);
  }

  public void setVolumePerformanceJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.volumePerformance = null;
    } else {
      this.volumePerformance = DataSerializer.JSON.fromString(json, MapObject.class);
    }
  }

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.LAZY)
  @Column(name = "forecast", length = 64 * 1024)
  public String getForecastJson() {
    if (this.forecast == null)
      return null;
    return DataSerializer.JSON.toString(this.forecast);
  }

  public void setForecastJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.forecast = null;
    } else {
      this.forecast = DataSerializer.JSON.fromString(json, MapObject.class);
    }
  }

  @JsonIgnore
  @Access(AccessType.PROPERTY)
  @Basic(fetch = FetchType.LAZY)
  @Column(name = "highlights", length = 64 * 1024)
  public String getHighlightsJson() {
    if (this.highlights == null)
      return null;
    return DataSerializer.JSON.toString(this.highlights);
  }

  public void setHighlightsJson(String json) {
    if (StringUtil.isEmpty(json)) {
      this.highlights = null;
    } else {
      this.highlights = DataSerializer.JSON.fromString(json, MapObject.class);
    }
  }

  public String generateCodePrefix(String loginId) {
    if (loginId == null || reportedDateFrom == null || reportedDateTo == null) {
      return null;
    }
    String upperLoginId = loginId.toUpperCase();
    String fromDate = DateUtil.asCompactDateId(reportedDateFrom);
    String toDate = DateUtil.asCompactDateId(reportedDateTo);
    String fromMonth = fromDate.substring(4, 6);
    String fromDay = fromDate.substring(6, 8);
    String toMonth = toDate.substring(4, 6);
    String toDay = toDate.substring(6, 8);
    return upperLoginId + "_" + fromDay + fromMonth + toDay + toMonth + "-1";
  }

}