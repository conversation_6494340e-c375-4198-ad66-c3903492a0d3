package cloud.datatp.fforwarder.sales.report;

import cloud.datatp.bfsone.partner.BFSOnePartnerLogic;
import cloud.datatp.bfsone.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import cloud.datatp.fforwarder.sales.report.entity.SalesDailyTask;
import cloud.datatp.fforwarder.sales.report.entity.SalesDailyTask.SalesTaskStatus;
import cloud.datatp.fforwarder.sales.report.repository.SalesDailyTaskRepository;
import cloud.datatp.fforwarder.settings.message.CRMMessageLogic;
import cloud.datatp.fforwarder.settings.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.settings.message.entity.MessageType;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.core.security.entity.DataScope;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskApprovalStatus;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Getter
public class SaleTaskReportLogic extends DAOService {

  @Autowired
  private SalesDailyTaskRepository dailyTaskRepo;

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private UploadService uploadService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private InquiryRequestLogic inquiryReqLogic;

  @Autowired
  private CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private BFSOnePartnerLogic bfsonePartnerLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private SalesDailyTaskPlugin taskPlugin;


  //   ---------------------- Sales Daily Tasks ----------------------

  public SalesDailyTask getSalesDailyTaskById(ClientInfo client, Company company, Long id) {
    return dailyTaskRepo.findById(id).get();
  }

  public List<SalesDailyTask> findYesterdayUncompletedTasks(ClientInfo client, Company company) {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    cal.set(Calendar.HOUR_OF_DAY, 0);
    cal.set(Calendar.MINUTE, 0);
    cal.set(Calendar.SECOND, 0);
    cal.set(Calendar.MILLISECOND, 0);
    Date startDate = cal.getTime();
    cal.add(Calendar.DATE, 1);
    Date endDate = cal.getTime();
    return dailyTaskRepo.findYesterdayUncompletedTasks(company.getId(), startDate, endDate);
  }

  public SalesDailyTask handleSalesDailyTask(ClientInfo client, Company company, SalesDailyTask saleTask) {
    EntityTask entityTask = new EntityTask();
    if (!saleTask.isNew()) {
      List<EntityTask> entityTasks = taskPlugin.findByEntityId(client, company, SalesDailyTask.TABLE_NAME, saleTask.getId());
      if (Collections.isNotEmpty(entityTasks)) entityTask = entityTasks.get(0);
    } else {
      saleTask.setEntityTaskRequest(new EntityTaskRequest());
    }

    computeEntityTaskData(entityTask, saleTask);

    taskPlugin.handle(client, company, saleTask, entityTask, EntityTaskStatus.Submitted);

    return saleTask;
  }

  private void computeEntityTaskData(EntityTask entityTask, SalesDailyTask saleTask) {
    if (StringUtil.isNotEmpty(saleTask.getLabel())) {
      entityTask.setLabel(saleTask.getLabel());
    } else {
      entityTask.setLabel("N/A");
    }

    entityTask.setStatus(EntityTaskStatus.Submitted);
    entityTask.setApprovalStatus(EntityTaskApprovalStatus.Approved);
    entityTask.setTaskType("SALES");
    entityTask.setDueDate(saleTask.getDueDate());
    entityTask.setDeadline(saleTask.getDueDate());
    entityTask.setAssigneeAccountId(saleTask.getCreatorAccountId());
    entityTask.setAssigneeFullName(saleTask.getCreatorLabel());
    entityTask.setReporterAccountId(saleTask.getSalemanAccountId());
    entityTask.setReporterFullName(saleTask.getSalemanLabel());
  }

  public SalesDailyTask saveSalesDailyTask(ClientInfo client, Company company, SalesDailyTask task) {
    boolean isNotiEnabled = task.isSendingZalo() || task.isSendingEmail();
    boolean isNewTask = task.isNew();
    boolean hasNotificationTime = task.getNotificationTime() != null;

    if (task.isNew()) {
      if (task.getCreatedDate() == null) {
        task.setCreatedDate(new Date());
      }
      if (task.getCreatorAccountId() == null) {
        Account account = accountLogic.getActiveAccountByLoginId(client, client.getRemoteUser());
        Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
        task.setCreatorAccountId(account.getId());
        task.setCreatorLabel(account.getFullName());
      }
      if (task.getDueDate() == null) task.initializeDueDate();

      if (task.getCustomerLeadPartnerId() != null) {
        CustomerLeads customerLead = customerLeadsLogic.getCustomerLeadById(client, company, task.getCustomerLeadPartnerId());
        PartnerEventHistory eventHistory = new PartnerEventHistory(customerLead);
        eventHistory.withSalesDailyTask(task);
        customerLeadsLogic.savePartnerEventHistory(client, company, eventHistory);
      }

      if (task.getCustomerPartnerId() != null) {
        BFSOnePartner customer = bfsonePartnerLogic.getById(client, company, task.getCustomerPartnerId());
        PartnerEventHistory eventHistory = new PartnerEventHistory(customer);
        eventHistory.withSalesDailyTask(task);
        customerLeadsLogic.savePartnerEventHistory(client, company, eventHistory);
      }

    } else {
      SalesDailyTask taskInDb = getSalesDailyTaskById(client, company, task.getId());
      SalesTaskStatus status = task.getStatus();
      if (!SalesTaskStatus.isCompleted(taskInDb.getStatus()) && SalesTaskStatus.isCompleted(status)) {
        task.setDueDate(new Date());
      }

    }

    task.set(client, company);
    SalesDailyTask saved = dailyTaskRepo.save(task);

    // Handle Zalo notification
    if (isNotiEnabled && hasNotificationTime) {

      if (!isNewTask) {
        SalesDailyTask taskInDb = getSalesDailyTaskById(client, company, task.getId());
        if (!taskInDb.getNotificationTime().equals(task.getNotificationTime())) {
          if (task.isSendingZalo()) {
            CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.ZALO);
            crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
          }

          if (task.isSendingEmail()) {
            CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.MAIL);
            crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
          }
        }
      } else {
        if (task.isSendingZalo()) {
          CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.ZALO);
          crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
        }
        if (task.isSendingEmail()) {
          CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.MAIL);
          crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
        }
      }
    }

    return saved;
  }

  public CRMMessageSystem createCRMMessageSystem(ClientInfo client, SalesDailyTask task, MessageType messageType) {
    CommunicationAccount communicationAccount = communicationMessageLogic.getCommunicationAccountByAccountId(client, task.getSalemanAccountId());

    CRMMessageSystem crmMessageSystem = new CRMMessageSystem();
    crmMessageSystem.setCompanyId(task.getCompanyId());
    crmMessageSystem.setScheduledAt(task.getNotificationTime());
    crmMessageSystem.setPluginName(SaleDailyTaskMessagePlugin.PLUGIN_TYPE);
    crmMessageSystem.setReferenceId(task.getId());
    crmMessageSystem.setReferenceType(SalesDailyTask.TABLE_NAME);
    crmMessageSystem.setMessageType(messageType);
    MapObject metadata = new MapObject();
    if (messageType == MessageType.ZALO) {
      crmMessageSystem.setContent(TaskNotificationTemplate.buildZaloTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getMobile()));
    } else if (messageType == MessageType.MAIL) {
      crmMessageSystem.setContent(TaskNotificationTemplate.buildMailTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getEmail()));
      metadata.put("subject", "CRM - Daily Task Notification");
      metadata.put("fromEmail", "<EMAIL>");
      metadata.put("ccList", "<EMAIL>");
    }
    crmMessageSystem.setMetadata(metadata);
    return crmMessageSystem;
  }

  public List<MapObject> saveSaleDailyTaskRecords(ClientInfo client, Company company, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject req : requests) {
        final Long id = req.getLong("id", null);
        SalesDailyTask task = new SalesDailyTask();
        if (id != null) {
          task = getSalesDailyTaskById(client, company, id);
          Objects.assertNotNull(task, "Sales Daily Task not found: id = " + id);
        }
        task = task.computeFromMapObject(req);
        SalesDailyTask updated = handleSalesDailyTask(client, company, task);
        req.put("id", updated.getId());
      }
    }
    return requests;
  }

  public List<SqlMapRecord> searchSalesDailyTasks(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();

    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      String scriptDir = appEnv.addonPath("core", "groovy");
      String scriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchDbRecords(client, scriptDir, scriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("participantAccountIds", participantAccountIds);
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SalesDailyTaskSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchSalesDailyTasks", sqlParams);
  }

  public boolean deleteSalesDailyTaskByIds(ClientInfo client, Company company, List<Long> ids) {
    dailyTaskRepo.deleteByIds(company.getId(), ids);
    return true;
  }

  public boolean changeDailyTaskStorageState(ClientInfo client, Company company, ChangeStorageStateRequest req) {
    dailyTaskRepo.setSaleDailyTaskState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<SqlMapRecord> saleAccountReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SaleAccountReport", sqlParams);
  }

  public List<SqlMapRecord> salemanSystemPerformanceReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    if(!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SalemanSystemPerformanceReport", sqlParams);
  }

  public List<SqlMapRecord> saleConversationRateReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    if(!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SaleConversationRateReport", sqlParams);
  }

}