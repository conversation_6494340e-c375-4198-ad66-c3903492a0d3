package cloud.datatp.fforwarder.sales.report;

import cloud.datatp.fforwarder.sales.report.entity.SalesDailyTask;

import java.util.List;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.common.ClientInfo;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component("SaleTaskReportService")
public class SaleTaskReportService extends BaseComponent {

  @Autowired
  private SaleTaskReportLogic taskReportLogic;

  // ---------------------- Sales Daily Tasks ----------------------
  @Transactional(readOnly = true)
  public SalesDailyTask getSalesDailyTaskById(ClientInfo client, Company company, Long id) {
    return taskReportLogic.getSalesDailyTaskById(client, company, id);
  }

  @Transactional
  public SalesDailyTask saveSalesDailyTask(ClientInfo client, Company company, SalesDailyTask task) {
    return taskReportLogic.saveSalesDailyTask(client, company, task);
  }

  @Transactional
  public SalesDailyTask handleSalesDailyTask(ClientInfo client, Company company, SalesDailyTask task) {
    return taskReportLogic.handleSalesDailyTask(client, company, task);
  }

  @Transactional
  public List<MapObject> saveSaleDailyTaskRecords(ClientInfo client, Company company, List<MapObject> records) {
    return taskReportLogic.saveSaleDailyTaskRecords(client, company, records);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchSalesDailyTasks(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    return taskReportLogic.searchSalesDailyTasks(client, company, sqlParams);
  }

  @Transactional
  public boolean deleteSalesDailyTaskByIds(ClientInfo client, Company company, List<Long> ids) {
    return taskReportLogic.deleteSalesDailyTaskByIds(client, company, ids);
  }

  @Transactional
  public boolean changeDailyTaskStorageState(ClientInfo client, Company company, ChangeStorageStateRequest req) {
    return taskReportLogic.changeDailyTaskStorageState(client, company, req);
  }

  // ---------------------- Customer/ Customer Lead Report----------------------

  @Transactional(readOnly = true)
  public List<SqlMapRecord> saleAccountReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    return taskReportLogic.saleAccountReport(client, company, sqlParams);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> salemanSystemPerformanceReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    return taskReportLogic.salemanSystemPerformanceReport(client, company, sqlParams);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> saleConversationRateReport(ClientInfo client, Company company, SqlQueryParams sqlParams) {
    return taskReportLogic.saleConversationRateReport(client, company, sqlParams);
  }

}