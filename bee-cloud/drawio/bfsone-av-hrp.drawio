<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36" version="27.0.9">
  <diagram name="Page-1" id="rvecp-LfN1xWRMMQkkwK">
    <mxGraphModel dx="946" dy="679" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="NIazheqpPaTdbYQJWSLs-41" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="50" y="230" width="910" height="850" as="geometry" />
        </mxCell>
        <mxCell id="22C_QzUwEv9oRwyfrfUw-77" value="AV, HRP, BFSOne Servers" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=18;" parent="1" vertex="1">
          <mxGeometry x="41" width="270" height="50" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-2" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="107" y="290" width="840" height="440" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-3" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="107" y="770" width="840" height="270" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-6" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="57" y="76" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="306" y="163" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-11" value="&lt;b&gt;VM&lt;/b&gt;(? cores, ?GB RAM, ?GB SSD)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="118" y="694" width="219" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-12" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="118" y="300" width="819" height="390" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-14" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;IIS&lt;/b&gt;&lt;/span&gt;(Http Web Server Gateway)" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="127" y="310" width="803" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-15" value="&lt;b&gt;IP Gateway&lt;/b&gt;(Open Port 80, 443, ???)" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="60" y="240" width="890" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-20" value="&lt;b&gt;VM&lt;/b&gt;(? cores, ?GB RAM, ?GB SSD)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="111" y="1010" width="219" height="27" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-33" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="335" y="360" width="188" height="310" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-36" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="337" y="500" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-39" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="335" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-53" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="541" y="360" width="180" height="310" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-54" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="541" y="360" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-55" value="HRP" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="549" y="360" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-56" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="547" y="500" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-58" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="547" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-59" value="- ????" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="548" y="411" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-61" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="131" y="360" width="188" height="310" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-70" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="NIazheqpPaTdbYQJWSLs-62" target="NIazheqpPaTdbYQJWSLs-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-60" value="CRM" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="336" y="355" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-62" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="129" y="526" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-64" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="131" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-65" value="- ???&lt;div&gt;- ????&lt;/div&gt;&lt;div&gt;- ?????&lt;/div&gt;&lt;div&gt;- ??????&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="133" y="410" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-66" value="BFSOne" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="132" y="355" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-68" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0.025;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;endSize=3;startSize=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="102" y="464" as="sourcePoint" />
            <mxPoint x="72" y="274" as="targetPoint" />
            <Array as="points">
              <mxPoint x="71" y="464" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-69" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;endSize=3;startSize=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="71" y="463" as="sourcePoint" />
            <mxPoint x="108" y="914" as="targetPoint" />
            <Array as="points">
              <mxPoint x="71" y="913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-76" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1.005;entryY=0.089;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="131" y="388" as="sourcePoint" />
            <mxPoint x="319.94000000000005" y="387.9200000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-77" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1.005;entryY=0.089;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="335" y="388" as="sourcePoint" />
            <mxPoint x="524" y="388" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-85" value="HRP" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="330" y="827" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-86" value="HRP Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-85" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-91" value="BFSOne DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=23;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="557" y="827" width="190" height="53" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-92" value="BFS One Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-91" vertex="1">
          <mxGeometry y="23" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-93" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;MS SQL Database Server&lt;/b&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="119" y="780" width="821" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-95" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.121;exitY=-0.067;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="NIazheqpPaTdbYQJWSLs-15" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="169" y="228" as="sourcePoint" />
            <mxPoint x="167" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-1" value="AV DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="121" y="827" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-2" value="AV Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-1" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-3" value="- ???&lt;div&gt;- ????&lt;/div&gt;&lt;div&gt;- ?????&lt;/div&gt;&lt;div&gt;- ??????&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="339" y="415" width="180" height="70" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-4" value="- ???&lt;div&gt;- ????&lt;/div&gt;&lt;div&gt;- ?????&lt;/div&gt;&lt;div&gt;- ??????&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="137.5" y="540" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-5" value="- ???&lt;div&gt;- ????&lt;/div&gt;&lt;div&gt;- ?????&lt;/div&gt;&lt;div&gt;- ??????&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="343" y="526" width="180" height="94" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-6" value="- ???&lt;div&gt;- ????&lt;/div&gt;&lt;div&gt;- ?????&lt;/div&gt;&lt;div&gt;- ??????&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="549" y="526" width="161" height="84" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-7" value="AV" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="106" y="48" width="140" height="120" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-8" value="Plugin Code 1?" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-7" vertex="1">
          <mxGeometry y="30" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-9" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Plugin Code 2?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-7" vertex="1">
          <mxGeometry y="60" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-10" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Plugin Code 3?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-7" vertex="1">
          <mxGeometry y="90" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-12" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="268" y="77" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-13" value="HRP" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="317" y="49" width="140" height="120" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-14" value="Plugin Code 1?" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-13" vertex="1">
          <mxGeometry y="30" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-15" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Plugin Code 2?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-13" vertex="1">
          <mxGeometry y="60" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-16" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Plugin Code 3?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-13" vertex="1">
          <mxGeometry y="90" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-17" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="476" y="78" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-18" value="BFS One" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="525" y="50" width="140" height="120" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-19" value="Module 1?" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-18" vertex="1">
          <mxGeometry y="30" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-20" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Module 2?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-18" vertex="1">
          <mxGeometry y="60" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-21" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;Module 3?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-18" vertex="1">
          <mxGeometry y="90" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-26" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="690" y="78" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-27" value="Others" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="739" y="50" width="140" height="120" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-28" value="Browsers(CRM, HRP, BFS One)" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-27" vertex="1">
          <mxGeometry y="30" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-29" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;App ?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-27" vertex="1">
          <mxGeometry y="60" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-30" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(236, 236, 236); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;&quot;&gt;App?&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="ZuI0Qb6P5zS2Xg9bV27g-27" vertex="1">
          <mxGeometry y="90" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-31" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.121;exitY=-0.067;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="385" y="238" as="sourcePoint" />
            <mxPoint x="384" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-32" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.121;exitY=-0.067;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="595" y="239" as="sourcePoint" />
            <mxPoint x="594" y="171" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ZuI0Qb6P5zS2Xg9bV27g-33" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;exitX=0.121;exitY=-0.067;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="811" y="240" as="sourcePoint" />
            <mxPoint x="810" y="172" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="vyW2vvfmNcD9gxuA5DWQ-1" value="&lt;h1 style=&quot;margin-top: 0px;&quot;&gt;Others&lt;/h1&gt;&lt;div&gt;- Sourcesafe&lt;/div&gt;&lt;div&gt;- Company website&lt;/div&gt;&lt;div&gt;- File storage, backup&lt;/div&gt;&lt;div&gt;- MS Teams&lt;/div&gt;&lt;div&gt;- MS Email&lt;/div&gt;&lt;div&gt;- Others&lt;/div&gt;" style="text;html=1;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="50" y="1100" width="180" height="200" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
