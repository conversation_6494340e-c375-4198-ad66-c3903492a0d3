<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.9 Chrome/134.0.6998.205 Electron/35.4.0 Safari/537.36" version="27.0.9">
  <diagram name="Page-1" id="rvecp-LfN1xWRMMQkkwK">
    <mxGraphModel dx="946" dy="679" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="NIazheqpPaTdbYQJWSLs-41" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="50" y="230" width="910" height="850" as="geometry" />
        </mxCell>
        <mxCell id="22C_QzUwEv9oRwyfrfUw-77" value="Beelogistics Cloud Servers" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=18;" parent="1" vertex="1">
          <mxGeometry x="80" y="20" width="270" height="50" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-2" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="107" y="290" width="840" height="440" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-3" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="107" y="770" width="840" height="270" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-6" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="100" y="133" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-7" value="Browsers" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="140" y="133" width="88" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-8" value="" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="240" y="133" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-18" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="NIazheqpPaTdbYQJWSLs-9" target="NIazheqpPaTdbYQJWSLs-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-9" value="SSH" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.multi-document;whiteSpace=wrap;" parent="1" vertex="1">
          <mxGeometry x="292" y="133" width="88" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-11" value="&lt;b&gt;VM&lt;/b&gt;(8 cores, 16GB RAM, 100GB SSD)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="118" y="694" width="219" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-12" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="118" y="300" width="819" height="390" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-13" value="&lt;b&gt;k8S&lt;/b&gt;(container pod management)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="123" y="656" width="219" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-14" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;Nginx Pod&lt;/font&gt;&lt;/b&gt;(Http Web Server Gateway)" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="127" y="310" width="803" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-15" value="&lt;b&gt;IP Gateway&lt;/b&gt;(Open Port 80, 443, 22, pod ssh 30000 - 40000)" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="60" y="240" width="890" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-20" value="&lt;b&gt;VM&lt;/b&gt;(4 cores, 8GB RAM, 100GB SSD)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="111" y="1007" width="219" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-33" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="335" y="360" width="188" height="280" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-36" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="337" y="500" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-38" value="- Python 3.10 or above&lt;div&gt;-&amp;nbsp;pandas lib&lt;/div&gt;&lt;div&gt;-&amp;nbsp;pdfplumber&lt;/div&gt;&lt;div&gt;-&amp;nbsp;scikit-learn&lt;/div&gt;&lt;div&gt;- redis&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="339" y="530" width="180" height="100" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-39" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="335" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-40" value="- Xlsx Converter And Extractor&lt;div&gt;- VN Invoice Info Extractor&lt;/div&gt;&lt;div&gt;- PDF Merger, Extractor&lt;/div&gt;&lt;div&gt;- Redis Tmp Storage&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="337" y="406" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-43" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="741" y="360" width="180" height="280" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-44" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="741" y="360" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-45" value="Beescs Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="749" y="360" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-46" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="747" y="500" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-47" value="- Python 3.10 or above&lt;div&gt;- Odoo 14 Community Version&lt;/div&gt;&lt;div&gt;- Postgres DB 16&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="749" y="521" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-48" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="747" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-49" value="- Odoo Module Accounting" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="750" y="405" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-53" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="541" y="360" width="180" height="280" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-54" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="541" y="360" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-55" value="LMS Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="549" y="360" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-56" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="547" y="500" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-57" value="- PHP 8.1&lt;div&gt;- Moodle&lt;/div&gt;&lt;div&gt;- Postgres DB 16&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="549" y="519" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-58" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="547" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-59" value="- Moodle LMS" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="548" y="411" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-61" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="131" y="360" width="188" height="280" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-70" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="NIazheqpPaTdbYQJWSLs-62" target="NIazheqpPaTdbYQJWSLs-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-60" value="MSA Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="336" y="355" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-62" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="129" y="526" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-63" value="- Java + Spring Framework&lt;div&gt;- JS + React JS + Typescript&lt;/div&gt;&lt;div&gt;- MSA - Micro Service Architecture - Framework&lt;/div&gt;&lt;div&gt;- Postgres DB 16&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="131" y="547" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-64" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="131" y="385" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-65" value="- DataTP Core Module&lt;div&gt;- DataTP CRM&lt;/div&gt;&lt;div&gt;- DataTP TMS&lt;/div&gt;&lt;div&gt;- DataTP Forwarder Managent&lt;/div&gt;&lt;div&gt;- DataTP OKR/KPI&lt;/div&gt;&lt;div&gt;- DataTP Document IE&lt;/div&gt;&lt;div&gt;- Webui Controller&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="133" y="410" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-66" value="DataTP Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="132" y="355" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-68" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0.025;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;endSize=3;startSize=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="102" y="464" as="sourcePoint" />
            <mxPoint x="72" y="274" as="targetPoint" />
            <Array as="points">
              <mxPoint x="71" y="464" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-69" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;endSize=3;startSize=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="71" y="463" as="sourcePoint" />
            <mxPoint x="108" y="914" as="targetPoint" />
            <Array as="points">
              <mxPoint x="71" y="913" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-76" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1.005;entryY=0.089;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="131" y="388" as="sourcePoint" />
            <mxPoint x="319.94000000000005" y="387.9200000000001" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-77" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1.005;entryY=0.089;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="335" y="388" as="sourcePoint" />
            <mxPoint x="524" y="388" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-78" value="DataTP DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="118" y="827" width="190" height="180" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-79" value="Core Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-78" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-80" value="TMS Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-78" vertex="1">
          <mxGeometry y="60" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-83" value="CRM Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-78" vertex="1">
          <mxGeometry y="90" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-81" value="OKR/KPI Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-78" vertex="1">
          <mxGeometry y="120" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-84" value="Forwarder Management Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-78" vertex="1">
          <mxGeometry y="150" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-85" value="LMS Moodle DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="330" y="827" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-86" value="Moodle Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-85" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-91" value="Beescs DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="557" y="827" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-92" value="Odoo Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="NIazheqpPaTdbYQJWSLs-91" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-93" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;Postgres Database Server&lt;/b&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="119" y="780" width="821" height="30" as="geometry" />
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-94" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0.67;entryY=0.85;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="NIazheqpPaTdbYQJWSLs-41" target="NIazheqpPaTdbYQJWSLs-7" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="470" as="sourcePoint" />
            <mxPoint x="210" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="NIazheqpPaTdbYQJWSLs-95" value="" style="endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0.67;entryY=0.85;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.164;exitY=0.012;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="342" y="240" as="sourcePoint" />
            <mxPoint x="342" y="184" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-6" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" parent="1" vertex="1">
          <mxGeometry x="49" y="1136" width="911" height="764" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-7" value="&lt;b&gt;Developer Server&lt;/b&gt;(8 cores, 64GB RAM, 256GB SSD)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="60" y="1859" width="340" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-8" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="59" y="1151" width="881" height="699" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-9" value="&lt;b&gt;k8S&lt;/b&gt;(container pod management)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="65" y="1812" width="219" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-10" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;Nginx Pod&lt;/font&gt;&lt;/b&gt;(Http Web Server Gateway)" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="69" y="1156" width="861" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-11" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="758" y="1206" width="170" height="280" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-12" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="760" y="1346" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-13" value="- Python 3.10 or above&lt;div&gt;-&amp;nbsp;pandas lib&lt;/div&gt;&lt;div&gt;-&amp;nbsp;pdfplumber&lt;/div&gt;&lt;div&gt;-&amp;nbsp;scikit-learn&lt;/div&gt;&lt;div&gt;- redis&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="762" y="1376" width="180" height="100" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-14" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="758" y="1231" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-15" value="- Xlsx Converter And Extractor&lt;div&gt;- VN Invoice Info Extractor&lt;/div&gt;&lt;div&gt;- PDF Merger, Extractor&lt;/div&gt;&lt;div&gt;- Redis Tmp Storage&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="760" y="1252" width="180" height="85" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-30" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="573" y="1206" width="176" height="280" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-31" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="zfg6F6oceOesh5kUdaA3-33" target="zfg6F6oceOesh5kUdaA3-36" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-32" value="MSA Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="759" y="1201" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-33" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="571" y="1372" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-34" value="- Java + Spring Framework&lt;div&gt;- JS + React JS + Typescript&lt;/div&gt;&lt;div&gt;- MSA - Micro Service Architecture - Framework&lt;/div&gt;&lt;div&gt;- Postgres DB 16&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="573" y="1393" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-35" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="573" y="1231" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-36" value="- DataTP Core Module&lt;div&gt;- DataTP CRM&lt;/div&gt;&lt;div&gt;- DataTP TMS&lt;/div&gt;&lt;div&gt;- DataTP Forwarder Managent&lt;/div&gt;&lt;div&gt;- DataTP OKR/KPI&lt;/div&gt;&lt;div&gt;- DataTP Document IE&lt;/div&gt;&lt;div&gt;- Webui Controller&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="575" y="1256" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-37" value="DataTP Pod 2" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="574" y="1201" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-38" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="573" y="1234" as="sourcePoint" />
            <mxPoint x="749" y="1234" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-39" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="758" y="1234" as="sourcePoint" />
            <mxPoint x="928" y="1234" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-40" value="" style="rounded=0;whiteSpace=wrap;html=1;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" parent="1" vertex="1">
          <mxGeometry x="74" y="1514" width="840" height="270" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-41" value="&lt;b&gt;VM&lt;/b&gt;(4 cores, 8GB RAM, 100GB SSD)" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="78" y="1751" width="219" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-42" value="DataTP DB 1" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="85" y="1571" width="190" height="180" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-43" value="Core Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-42" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-44" value="TMS Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-42" vertex="1">
          <mxGeometry y="60" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-45" value="CRM Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-42" vertex="1">
          <mxGeometry y="90" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-46" value="OKR/KPI Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-42" vertex="1">
          <mxGeometry y="120" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-47" value="Forwarder Management Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-42" vertex="1">
          <mxGeometry y="150" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-48" value="LMS Moodle DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
          <mxGeometry x="490" y="1571" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-49" value="Moodle Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-48" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-50" value="Beescs DB" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="717" y="1571" width="190" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-51" value="Odoo Tables" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-50" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-52" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;Postgres DB Pod&lt;/b&gt;&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="86" y="1524" width="821" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-53" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="388" y="1207" width="175" height="280" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-54" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="zfg6F6oceOesh5kUdaA3-55" target="zfg6F6oceOesh5kUdaA3-58" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-55" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="386" y="1373" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-56" value="- Java + Spring Framework&lt;div&gt;- JS + React JS + Typescript&lt;/div&gt;&lt;div&gt;- MSA - Micro Service Architecture - Framework&lt;/div&gt;&lt;div&gt;- Postgres DB 16&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="388" y="1394" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-57" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="388" y="1232" width="182" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-58" value="- DataTP Core Module&lt;div&gt;- DataTP CRM&lt;/div&gt;&lt;div&gt;- DataTP TMS&lt;/div&gt;&lt;div&gt;- DataTP Forwarder Managent&lt;/div&gt;&lt;div&gt;- DataTP OKR/KPI&lt;/div&gt;&lt;div&gt;- DataTP Document IE&lt;/div&gt;&lt;div&gt;- Webui Controller&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="390" y="1257" width="180" height="120" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-59" value="DataTP Pod 1" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="389" y="1202" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-60" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="388" y="1235" as="sourcePoint" />
            <mxPoint x="563" y="1235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-63" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="70" y="1209" width="140" height="280" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-65" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="68" y="1375" width="141" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-66" value="- Gitlab Community Version&lt;div&gt;- Embeded Postgres&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="70" y="1396" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-67" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="70" y="1231" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-68" value="- Git Services&lt;div&gt;- Gitlab webui service&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="72" y="1248" width="128" height="120" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-69" value="Gitlab Pod" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="71" y="1204" width="129" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-70" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=1;entryDx=0;entryDy=0;" parent="1" target="zfg6F6oceOesh5kUdaA3-69" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="1233" as="sourcePoint" />
            <mxPoint x="200" y="1233" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-77" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="231" y="1210" width="140" height="280" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-78" value="Dependencies:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="229" y="1376" width="141" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-79" value="- Gitlab Community Version" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="231" y="1397" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-80" value="Services:" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="233" y="1233" width="78" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-81" value="- Git Services&lt;div&gt;- Gitlab webui service&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="233" y="1249" width="148" height="120" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-82" value="Other Pods" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="232" y="1205" width="180" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-83" value="" style="endArrow=none;html=1;rounded=0;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" parent="1" target="zfg6F6oceOesh5kUdaA3-82" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="231" y="1234" as="sourcePoint" />
            <mxPoint x="361" y="1234" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-84" value="DataTP DB 2" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;verticalAlign=middle;" parent="1" vertex="1">
          <mxGeometry x="284" y="1571" width="190" height="180" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-85" value="Core Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-84" vertex="1">
          <mxGeometry y="30" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-86" value="TMS Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-84" vertex="1">
          <mxGeometry y="60" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-87" value="CRM Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-84" vertex="1">
          <mxGeometry y="90" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-88" value="OKR/KPI Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-84" vertex="1">
          <mxGeometry y="120" width="190" height="30" as="geometry" />
        </mxCell>
        <mxCell id="zfg6F6oceOesh5kUdaA3-89" value="Forwarder Management Module" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="zfg6F6oceOesh5kUdaA3-84" vertex="1">
          <mxGeometry y="150" width="190" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
