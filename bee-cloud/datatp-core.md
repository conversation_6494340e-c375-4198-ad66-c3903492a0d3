
# I. THÔNG TIN CHUNG

Người được phỏng vấn: 
- <PERSON><PERSON><PERSON><PERSON>.

Thời gian triển khai
- 7/2020 - curent

<PERSON><PERSON><PERSON> sự triển khai:
- <PERSON>uyễn <PERSON>h
- Đàn 
- Chiến
- Nhật
- Hiếu - đã thôi việc từ 2022
- Lâm  - đã thôi việc từ 2023

C<PERSON><PERSON> chức năng chính:
- Là nền tảng chung của hệ thống (mô tả rõ hơn các phần dùng chung: chung database, chung hạ tầng...?)
- Nền tảng chung cho quản trị user, employees, công việc.
- <PERSON><PERSON><PERSON> thư viện, service dùng chung
- Các service giám sát monitor các activity người dùng, và các activities của hệ thống.

Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
  * Đ<PERSON><PERSON> là nền tảng chung được sử dụng bởi các module kh<PERSON><PERSON> nh<PERSON>, TMS, OKR/KPI...
- <PERSON><PERSON> lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?
  * Toàn bộ nhân viên văn  phòng tại HP sử dụng 
  * 1 phần nhân viên khối sale, cus của Hà Nội, Đà Nẵng, HCM.

# II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
  * N/A
- Mô tả các workflow
  * N/A
  
# III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình: Java v21, groovy scripting language v4.0.20
    + Framework/thư viện: springframework v3.3.3
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình: javascript, html/css,
    + Framework/thư viện: bootstrap V5.3, reactjs V18.3.1
Cơ sở dữ liệu:
- SQL
  * PostgresQL 16.1
- Mô tả kiến trúc và cấu trúc CSDL?
  * Không có thiết kế tổng thể, chỉ có 1 số quy định chung về cách đặt tên bảng, tên trường, các trường cơ bản bắt buộc bảng nào cũng phải có như created_by, created_time, modified_by, modified_time, company_id, storage_state... Các tên bảng cùng module có prefix như account_xxx, hr_xxxx, tmx_xxxxx.
  * Khi có yêu cầu cho từng module, team mới thiết kế sơ bộ.
  * Team dùng chiến lược adative strategy, vừa làm vừa rút kinh nghiệm và có phát triển các công cụ hỗ trợ cho phép thay đổi linh hoạt bằng các cách như thêm bảng, xoá bảng, tạo lại các liên kết hay migrate dữ liệu.
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
  * Hiôn chỉ có Tuấn và Đàn có quyền truy cập vào máy chủ và DB để làm các việc như nâng cấp, cập nhật.
  * Chiến cũng được đào tạo và hướng dẫn làm việc này, nhưng không được giao quyền.
- Các cơ chế phân quyền
  * N/A
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
  * Tài khoản đăng nhập máy chủ.
- Cơ chế xác thực: 2FA, OTP?
  * Máy chủ dùng cơ chế ssh + username/password.
  * Máy DB dùng cơ chế ssh + username/password +  Google Authenticator (2FA)
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
  * Hiện dữ liệu của hệ thống chưa lớn(khoàng 5GB dữ liệu nén), nên mỗi khi cập nhật, quản trị viên chạy backup script backup lại toàn bộ dữ liệu. Mất khoảng 6 - 8 phút) 
  * 3 bản backup mới nhất được giữ lại.
  * Ngoài ra đi kèm với backup thủ công. Dịch vụ VM có backup lại ổ cứng tự động các ngày 2 - 6 hàng đêm. 
- Hình thức sao lưu (local, cloud...) sao lưu tự động
  * Thủ công, bản sao để trên máy chủ
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
    + Dùng chung máy ảo server 8 core, 16GB RAM, 100GB storage
    + Dùng chung máy ảo DB 4 core, 8GB RAM, 100GB storage
- Phần mềm platform: Linux Debian v12, database Postgresql v16.1, Kubernettes v1.33.2
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

# IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain
- Tất cả module quan trọng đang phát triển như CRM, TMS... đều phụ thuộc vào module này.
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
  * Phần này là phần core nên chỉ có các tài liệu hướng dẫn developer setup để phát triển.
  * Developer phải tự đọc code để hiểu hoặc đi hỏi.
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)
  * Có phát triển hệ thống activity monitor, theo dõi các activity của người dùng theo từng tennant, khung giờ, service sử dụng...
  * Các activity được phân loại như người dùng, bot...
  * Dựa vào hệ thống log.

# V. HƯỚNG PHÁT TRIỂN
- Vì đây là phần core nên vấn bỏ nhiều công sức để duy trì, phát triển và nâng cấp.
- Kế hoach 3 tháng tới là chuyển nền tảng authentication sang dùng chung hạ tầng SSO của Keycloak
- Kế hoạch 6 tháng tới là tuning nâng cấp hệ thống activity monitor.
- Tích hợp các hệ thống khác
- Tối ưu kiến trúc hệ thống hiện tại
- Tối ưu các phần tử, các điểm nghẽn...
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.

# VI. CÁC THÔNG TIN KHÁC
Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ

