# Tiền dự án.
- <PERSON><PERSON><PERSON> cầu cấp thiết hay ý tưởng.
  * <PERSON><PERSON><PERSON> cầu cấp thiết => bắt buộc phải làm. 
  * Ý tưởng => nên thuyết phục lãnh đạo hoặc tài trợ. Nên chọn một số phần chính của ý tưởng làm xem có khả thi.
  
- <PERSON><PERSON>u cầu cho dự án:
  * Số người tham gia. 
  * Thời gian.
  * <PERSON><PERSON> liên quan, ảnh hưởng đến các phần cũ, hoặc các quy trình làm việc hiện tại.
  * <PERSON><PERSON> điểm nghẽn không như chỉ có một người có khả năng quản lý đấu nối với hệ thống cũ mà người đó không chịu làm.
  * ...
  * <PERSON><PERSON>h giá khả thi.
- <PERSON><PERSON> hoạch phát triển phase 1:
  * Ai tham gia
  * M<PERSON>t bao lâu.
  * <PERSON> thử nghiệm, mất bao lâu.
  * <PERSON><PERSON> thể triển khai đại trà ngay không.
  * <PERSON><PERSON><PERSON> giá khả thi.
- Kế hoạch phát triển phase 2:
  * Ai tham gia
  * Mất bao lâu.
  * Ai thử nghiệm, mất bao lâu.
  * Có thể triển khai đại trà ngay không.
  * Đánh giá khả thi.

# Chiến lược và tiêu chí phát triển dự án.

- Dùng cơ chế phân tán.
- Mỗi service, app làm đúng nhiệm vụ của nó.
- Dùng các công nghệ, ngôn ngữ lập trình, thiết hện đang là su hướng. Ví dụ python, nodejs, reactjs, micro service...
- Có thể tích hợp với các service phổ biến như Keycloak SSO, Google authentication, MS Authentication, FB Authentication.
- Có thể tận dụng tích hợp với các service, ứng dụng mã nguổn mở hay, hay commercial như CMS, OCR, AI ...
- Co thẻ triển khai dưới dạng stateless application không, đây là 1 tiêu chí quan trọng để biết khả năng scalability và reliability của phần mềm.
- Phân tích đánh giá các module, service xem có phần nào khó, xác xuất không đạt tiêu chí chất lượng, hoặc các phần nào không bị phụ thuộc, có khả năng hoạt động độc lập.
- Bắt buộc hoặc ưu tiên làm phần khó trước, vì trong dự án nếu phần khó không làm được hoặc phần khó không đạt chất lượng, các phần khác làm được cũng không dùng làm gì. Ví dụ OCR nhận dạng văn bản không làm được các phần đạt độ chính xác trên 90% hay giá thành rẻ khi phát sinh mẫu mới thì không nên làm phần giao diện cho người dùng.
- Ưu tiên làm các phần không phụ thuộc trước để có thề đưa ra giai đoạn thử nghiệm phase 1, càng sớm càng tốt.
