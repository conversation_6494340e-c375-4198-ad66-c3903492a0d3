# Team DataTP

Thành Viên:
- <PERSON><PERSON><PERSON><PERSON> (7/2020 - current)
- <PERSON><PERSON><PERSON>
- Đàn
- ...
- <PERSON><PERSON><PERSON>(2021 - 2022)
- <PERSON><PERSON><PERSON> (2021 - 2023)

Quản lý: <PERSON><PERSON><PERSON><PERSON>.
Team Leader: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.

# Các Dự Án <PERSON>ang <PERSON>uả<PERSON> Lý


## DataTP CRM
người được phỏng vấn: `<PERSON><PERSON>`

I. THÔNG TIN CHUNG
1. Thời gian triển khai
  `01/2024 -> Now`

2. Nhân sự triển khai:
  - `<PERSON><PERSON>n`: Contribute chính
  - `<PERSON><PERSON> Tường An`: Hỗ trợ
  - `Lê Quang Nhật`: Hỗ trợ
  - `<PERSON>uyễn Năng Bình`: Hỗ trợ (đã thôi việc từ 06/2024)

3. <PERSON><PERSON><PERSON> chứ<PERSON> năng chính (mô tả chi tiết các chức năng chính của hệ thống)

  - Module Pricing Tools: <PERSON><PERSON><PERSON><PERSON> lý giá cước vậ<PERSON> (sea, air, logistics, trucking)
  - Module Sales: <PERSON>u<PERSON><PERSON> lý quy trình b<PERSON> (lead, inquiry, quotation, booking)
  - Dashboard: Theo dõi hiệu suất và báo cáo KPI
  - Quản lý đối tác (Partner Management)
  - Theo dõi hoạt động hàng ngày của nhân viên kinh doanh (Daily Tasks)

4. Phạm vi triển khai:
  - Hệ thống được triển khai phục vụ nội bộ công ty
  - Số lượng người sử dụng theo thiết kế: Toàn bộ nhân viên sales và pricing/ bộ phận backoffice khác.
  - Số lượng người sử dụng thực tế hiện tại: 1074 người (dựa theo dữ liệu account được phân quyền vào CRM)

II. CHỨC NĂNG VÀ QUY TRÌNH NGHIỆP VỤ
- Danh sách chi tiết các module:
  1. Module Pricing Tools:
     - Quản lý bảng giá Sea FCL, Sea LCL, Air Transport, Trucking (Container / Truck/ CBT)
     - Tách bảng giá nhập/xuất theo mục đích sử dụng
     - Template cho Local Charge
     - Theo dõi công việc, tính kpi cho bộ phận Pricing dựa theo inquiry và số lượng đầu việc trên từng inquiry.

  2. Module Sales:
     - Quản lý khách hàng tiềm năng (Lead Management)
     - Quản lý khách hàng (Customer Management)
     - Theo dõi yêu cầu báo giá (Inquiry)
     - Tạo và gửi báo giá (Quotation)
     - Chuyển đổi báo giá thành booking, api với hệ thống BFSOne.

  3. Dashboard:
     - Company Pricing Dashboard
     - Sales Dashboard
     - Volume Performance by Department
     - Sales Activities Tracker

- Quy trình làm việc chính:
  1. Sales nhập inquiry vào hệ thống
  2. Hệ thống kiểm tra giá có sẵn hoặc gửi yêu cầu báo giá đến bộ phận pricing
  3. Tạo quotation và gửi cho khách hàng
  4. Xác nhận booking và đồng bộ với hệ thống OF1

- Liên kết với hệ thống CoreTP:
  - Sử dụng chung cơ sở dữ liệu về khách hàng, đối tác
  - Kế hoạch tách ra thành hệ thống độc lập với database riêng

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình: Java 21, Groovy
    + Framework/thư viện: Spring Boot, Spring Batch
    + Kiến trúc hệ thống: Monolithic với các module tách biệt
- Frontend:
    + Ngôn ngữ lập trình: JavaScript, HTML/CSS
    + Framework/thư viện: React

Cơ sở dữ liệu:
- PostgreSQL

Phân quyền:
- Sử dụng hệ thống phân quyền của DataTP Core
- Các vai trò: Admin, Moderator, Write, Read
- Scope dữ liệu: All, Company, Group, Owner.
- Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO

Sao lưu dữ liệu:
- Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core
- Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống

Hạ tầng tài nguyên hệ thống:
- Chạy trên nền tảng cloud chung với DataTP Core
- Kế hoạch tách thành hệ thống độc lập

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm đang được phát triển tích cực với các phiên bản mới hàng tháng
- Có tài liệu hướng dẫn sử dụng cho người dùng trên trang web `https://docs.beelogistics.cloud/`
- Đang xây dựng hệ thống giám sát và cảnh báo riêng

V. HƯỚNG PHÁT TRIỂN
- Kế hoạch 3 tháng tới:
  + Tách dự án thành hệ thống độc lập với database riêng
  + Tích hợp với Keycloak SSO
  + Hoàn thiện các màn hình và chức năng module sales
  + Cải thiện thông báo lỗi từ server đến người dùng

- Kế hoạch 6 tháng tới:
  + Tối ưu kiến trúc hệ thống
  + Phát triển thêm các tính năng marketing
  + Tích hợp đầy đủ với hệ thống OF1

- Cải tiến hệ thống:
  + Cập nhật UI/UX theo tiêu chuẩn mới
  + Tối ưu hiệu suất truy vấn cơ sở dữ liệu

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển:
- Thách thức trong việc xử lý dữ liệu từ nhiều nguồn và đồng bộ với hệ thống cũ
- Khó khăn trong việc thiết kế UI/UX đáp ứng nhu cầu của nhiều nhóm người dùng khác nhau
- Yêu cầu thay đổi thường xuyên từ người dùng

2. Phỏng vấn người dùng:
- Phần mềm sử dụng thường xuyên, mỗi ngày
- Phần mềm đáp ứng tốt nhu cầu quản lý giá và theo dõi quy trình bán hàng
- Cần cải thiện tốc độ và giao diện người dùng trên thiết bị di động
- Một số chức năng cần được đơn giản hóa quy trình

## DataTP Forwarder Management

Người được phỏng vấn: `Lê Ngọc Đàn`

I. THÔNG TIN CHUNG
1. Thời gian triển khai:
  - `06/2022 -> 11/2023`

2. Nhân sự triển khai:
  - `Nguyễn Tuấn Anh`
  - `Hà Trung Hiếu`
  - `Lê Ngọc Đàn`
  - `Lê Quang Nhật`

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
  - Quy trình vận hành, thay thế BFSOne.
  - Quản lý vận đơn (Master Bill, House Bill),
  - Quản lý Job file, bao gồm toàn bộ thông tin của 1 lô hàng.

4. Phạm vi triển khai:
- GD1: Phát triển và triển cho công ty HPS chạy thử nghiệm.
- Số lượng người sử dụng:
  - Kỳ vọng: toàn bộ nhân sự khối vấn hàng (Docs/ Cus) của BEE.

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ

III. KIẾN TRÚC VÀ CÔNG NGHỆ

1. Công nghệ sử dụng chính:
  - Backend:
    + Ngôn ngữ lập trình: Java 21, Groovy
    + Framework/thư viện: Spring Boot, Spring Batch
    + Kiến trúc hệ thống: Monolithic với các module tách biệt
  - Frontend:
    + Ngôn ngữ lập trình: JavaScript, HTML/CSS
    + Framework/thư viện: React

2. Cơ sở dữ liệu:
  - PostgreSQL

3. Phân quyền:
  - Sử dụng hệ thống phân quyền của DataTP Core
  - Các vai trò: Admin, Moderator, Write, Read
  - Scope dữ liệu: All, Company, Group, Owner.
  - Cơ chế đăng nhập: Thông qua DataTP Core, kế hoạch chuyển sang Keycloak SSO

4. Sao lưu dữ liệu:
  - Hiện tại sử dụng cơ chế sao lưu chung của hệ thống DataTP Core
  - Kế hoạch xây dựng cơ chế sao lưu riêng khi tách hệ thống

5. Hạ tầng tài nguyên hệ thống:
  - Chạy trên nền tảng cloud chung với DataTP Core
  - Kế hoạch tách thành hệ thống độc lập

IV. VẬN HÀNH VÀ KHAI THÁC
  - Dự án dùng và không tiếp tục phát triển vì các lý do khách quan không liên quan đến phần mềm như:
    - Công ty HPS tách khỏi Bee.
    - Api, liên kết dữ liệu với phần mềm kế toán (Công ty đang dùng AV - phần mềm kế toán cũ)

V. HƯỚNG PHÁT TRIỂN
VI. CÁC THÔNG TIN KHÁC


## DataTP TMS

Người được phỏng vấn: Chiến

I. THÔNG TIN CHUNG
1. Thời gian triển khai

2. Nhân sự triển khai:

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ


## DataTP OKR/KPI/Job Tracking

Người được phỏng vấn: Nhật.

I. THÔNG TIN CHUNG
1. Thời gian triển khai

2. Nhân sự triển khai:

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ

## DataTP Document IE

Người được phỏng vấn: Đạt Lương

I. THÔNG TIN CHUNG
1. Thời gian triển khai

2. Nhân sự triển khai:

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO và datatp-core để chạy độc lập hoàn toàn.
- Kế hoạch 6 tháng tới là Tích hợp với phần mềm CMS strapi để cung cấp đầy đủ dịch vụ CMS và document management.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án

2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ


## DataTP Spreadsheet

Người được phỏng vấn: Lê Ngọc Đàn + Chiến.

I. THÔNG TIN CHUNG
1. Thời gian triển khai: `16/10/2022`

2. Nhân sự triển khai:
  - `Lê Ngọc Đàn`
  - `Lê Quang Nhật`
  - `Phạm Minh Chiến`

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án
2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ

## Bee Legacy

Người được phỏng vấn: `Lê Ngọc Đàn`

I. THÔNG TIN CHUNG
1. Thời gian triển khai: `25/04/2025 -> Now`
2. Nhân sự triển khai: `Nguyễn Tuấn Anh` , `Lê Ngọc Đàn`

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)
>  - Tích hợp và đồng bộ dữ liệu từ hệ thống BFS One (hệ thống vận hành, kế toán)
>  - Quản lý tỷ giá hối đoái liên quan đến các loại báo cáo cho saleman trên hệ thống BFSOne (Exchange Rate)
>  - Quản lý thông tin vận đơn (House Bill), thu thập từ nhiều bảng trên database BFSOne (integrated_housebill)
>  - Quản lý thông tin partners tích hợp (integrated_partner)
>  - Quản lý thông tin unit (đơn vị).

4. Phạm vi triển khai:
  - Hệ thống được triển khai nội bộ để hỗ trợ chuyển đổi và tích hợp dữ liệu
  - Hệ thống phụ trợ, hỗ trợ tích hợp chức năng với hệ thống khác.
  - Người sử dụng chủ yếu là IT, Dev.

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
  - Các model chính:
    - Exchange Rate: Đồng bộ và quản lý tỷ giá hối đoái từ BFS One
    - House Bill: Tích hợp và quản lý thông tin vận đơn, thu thập từ nhiều bảng, nhiều nguồn ở BFSOne.
    - Integrated Partner: Quản lý thông tin đối tác, thu thập đẩy đủ thông tin partner (từ BFSOne, CRM)
    - Settings Unit: Quản lý cài đặt đơn vị
  - Tạo ra các bảng chung, thu thập dữ liệu từ nhiều nguồn, tối ưu cho mục đích báo cáo, trích rút dữ liệu thường sử dụng một cách nhanh chóng.
  - Các bảng này sẽ được sync tự động từ BFSOne, DataTP qua hình thức pull hoặc push (DataTP cập qua api)

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
  - Backend:
    - Ngôn ngữ lập trình: Python 3.10
    - Framework/thư viện: SQLAlchemy, pyodbc, click, PyYAML
    - Kiến trúc hệ thống: Module-based monolithic
  - Frontend: Không có giao diện người dùng riêng, tích hợp vào hệ thống khác
  - Cơ sở dữ liệu:
    - PostgreSQL cho lưu trữ dữ liệu chính
    - Kết nối đến Microsoft SQL Server (BFS One) để đồng bộ dữ liệu
    - Cấu trúc CSDL gồm các bảng chính: integrated_housebill, exchange_rate, integrated_partner, settings_unit
  - Phân quyền:
    - Không có hệ thống phân quyền riêng, phụ thuộc vào hệ thống chính
    - Truy cập cơ sở dữ liệu dựa trên tài khoản riêng được cấu hình trong file config.yaml
  - Sao lưu dữ liệu:
    - Sử dụng cơ chế sao lưu chung của hệ thống PostgreSQL
  - Hạ tầng tài nguyên hệ thống:
    - Chạy trên cùng hạ tầng với các dịch vụ khác của DataTP
    - Hệ điều hành: Linux Debian
    - Chia sẻ tài nguyên với các module khác của hệ thống

IV. VẬN HÀNH VÀ KHAI THÁC
  - Phần mềm được duy trì để hỗ trợ việc chuyển đổi, tập trung dữ liệu từ các hệ thống để tạo ra bộ dữ liệu chuẩn để các hệ thống cùng dùng chung.
  - Có các unit test để đảm bảo tính ổn định của hệ thống

V. HƯỚNG PHÁT TRIỂN
  - Kế hoạch 3 tháng tới:
    - Tích hợp với `Keycloak SSO` để quản lý xác thực
    - Làm việc với anh Quý để việc api với BFSOne mượt mà hơn.
    - Đối soát chéo với các hệ thống, triển khai một phần, đảm bảo dữ liệu là chính xác, đầy đủ.
  - Kế hoạch 6 tháng tới:
    - Nâng cấp khả năng đồng bộ dữ liệu từ nhiều nguồn
    - Tối ưu hiệu suất truy vấn và xử lý dữ liệu lớn
    - Thay thế các loại, màn hình báo cáo cho hệ thống DataTP Cloud, một số màn hình của BFSOne để tối ưu trải nghiệm cho người dùng.

  - Cải tiến kiến trúc:
    - Tăng độ phủ của unit test
    - Cải thiện hệ thống quản lý lỗi và log (grafana, prometheus, ..)

VI. CÁC THÔNG TIN KHÁC

  1. Phỏng vấn lập trình viên:
    - Thách thức chính là việc đồng bộ dữ liệu real time từ hệ thống BFS One (cũ) sang hệ thống mới.
    - Vấn đề về định dạng dữ liệu không đồng nhất giữa các hệ thống (dữ liệu phải phù hợp và thuận tiện cho cả BFSOne và DataTP Cloud)

  2. Phỏng vấn người dùng:
    - Hệ thống giúp giảm thời gian xử lý báo cáo từ nhiều nguồn dữ liệu
    - Cần cải thiện tốc độ đồng bộ dữ liệu và tính ổn định


## Beesco

Người được phỏng vấn: Nguyễn Tuấn Anh.

I. THÔNG TIN CHUNG
1. Thời gian triển khai

2. Nhân sự triển khai:

3. Các chức năng chính (mô tả chi tiết các chức năng chính của hệ thống)

4. Phạm vi triển khai:
- Hệ thống được triển khai ở đâu?
- Số lượng người sử dụng (theo thiết kế và theo thực tế hiện tại)?

II. CHỨC NĂNG VÀ QUY TRINH NGHIỆP VỤ
- Danh sách đầy đủ các module, chức năng cho từng nhánh công việc
- Mô tả các workflow
- Liên kết với hệ thống CoreTP thế nào

III. KIẾN TRÚC VÀ CÔNG NGHỆ
Công nghệ sử dụng chính:
- Backend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
    + Kiến trúc hệ thống: (Monolithic, Microservices, SOA...?)
- Frontend:
    + Ngôn ngữ lập trình:
    + Framework/thư viện:
Cơ sở dữ liệu:
- (MySQL, PostgreSQL, MongoDB...?)
- Mô tả kiến trúc và cấu trúc CSDL?
Phân quyền:
- Các loại người dùng (quản trị, người sử dụng, nhân viên...)
- Các cơ chế phân quyền
- Cơ chế đăng nhập: (tài khoản độc lập, SSO, LDAP...?)
- Cơ chế xác thực: 2FA, OTP?
Sao lưu dữ liệu
- Cơ chế sao lưu, backup dữ liệu, khả năng
- Hình thức sao lưu (local, cloud...) sao lưu tự động
Hạ tầng tài nguyên hệ thống:
- Loại hạ tầng của hệ thống: Cloud
- Hệ điều hành:
- Hạ tầng mạng
    + Ảo hóa, mạng vật lý
    + Tốc độ, băng thông giữa các thiết bị
- Bảo mật hệ thống
    + Có các hình thức ngăn chặn truy cập trái phép (firewall, IPS/IDS, VPN, filter...)
    + Có phân hệ thống thành các vùng riêng biệt (VLAN, VPN, DMZ..?)
    + Chống tấn công DDoS?

IV. VẬN HÀNH VÀ KHAI THÁC
- Phần mềm vẫn được duy trì phát triển và maintain thế nào
- Có tài liệu hướng dẫn sử dụng và vận hành chưa?
- Hệ thống giám sát, cảnh bảo hoạt động thế nào (tài nguyên hệ thống, lưu lượng, bảo mật, log...)

V. HƯỚNG PHÁT TRIỂN
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.
- Tích hợp các hệ thống khác?
- Tối ưu kiến trúc hệ thống hiện tại?
- Tối ưu các phần tử, các điểm nghẽn...?
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.?

VI. CÁC THÔNG TIN KHÁC
1. Phỏng vấn lập trình viên tham gia phát triển
- Trong quá trình triển khai, bạn gặp phải những vấn đề gì rủi ro cho thành công của dự án
2. Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- Đã sử dụng phần mềm với tàn xuất thế nào?
- Phần mềm có phù hợp với thực tế không?
- Phần mềm có những vấn đề gì cần hỗ trợ