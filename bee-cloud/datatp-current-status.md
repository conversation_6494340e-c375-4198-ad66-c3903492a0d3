# Team DataTP

Thành Viên:
- <PERSON><PERSON><PERSON><PERSON> (7/2020 - current)
- <PERSON><PERSON><PERSON>
- Đàn
- ...
- <PERSON><PERSON><PERSON>(2021 - 2022)
- <PERSON><PERSON><PERSON> (2021 - 2023)

<PERSON>uản lý: <PERSON><PERSON><PERSON><PERSON>.
Team Leader: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. 

# C<PERSON>c Dự Án <PERSON>ý

## DataTP Core

Thời gian
- 7/2020 - curent

<PERSON><PERSON><PERSON><PERSON> là<PERSON> ch<PERSON>h:
- <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Chiến...
- <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> (đã thôi việc từ 2023)

<PERSON><PERSON><PERSON> chức năng chính:
- <PERSON><PERSON> nền tảng chung của hệ thống
- <PERSON><PERSON><PERSON> nền tảng chung cho user, employees, công việc.
- ph<PERSON> quyền
- <PERSON><PERSON><PERSON> th<PERSON> viện , service dùng chung
- Các service theo dõi monitor các activity người dùng, và các activities của hệ thống.

Công nghệ sử dụng chính:
- Backend: Java v21, java springframework v3.3.3, groovy scripting language v4.0.20, SQL
- Frontend: javascript, html/css, bootstrap V5.3, reactjs V18.3.1

<PERSON><PERSON><PERSON> cứng, phần mềm, dịch vụ phụ thuộc dùng triển khai:
- Dùng chung máy ảo server 8 core, 16GB RAM, 100GB storage
- Dùng chung máy ảo DB 4 core, 8GB RAM, 100GB storage
- Hệ điều hành Linux Debian v12, database Postgresql v16.1, Kubernettes v1.33.2

Hiện trạng phần mềm
- Vẫn duy trì phát triển và maintain
- Tất cả module quan trọng đang phát triển như CRM, TMS... đều phụ thuộc vào module này.

Kế hoạch phát triển tiếp trong 6 tháng, 12 tháng, 3 năm....
- Vì đây là phần core nên vấn bỏ nhiều công sức để duy trì, phát triển và nâng cấp.
- Kế hoach 3 tháng tới là chuyển nền tảng authentication sang dùng chung hạ tầng SSO của Keycloak
- Kế hoạch 6 tháng tới là tuning nâng cấp hệ thống activity monitor.
- Loại bỏ hoặc viết lại 1 số code không đạt chuẩn.

Phỏng vấn lập trình viên tham gia phát triển
- abc

Phỏng vấn người dùng sử dụng, có đáp ứng được nhu cầu...
- abc

Người được phỏng vấn: Tuấn.

## DataTP CRM
Thời gian
- 7/2020 - 7/2022
- 1/2024 - curent

Kế hoạch phát triển tiếp trong 6 tháng, 12 tháng, 3 năm....
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.

Người được phỏng vấn: Đàn.

## DataTP Forwarder Management

Đàn trả lời.

## DataTP TMS

Kế hoạch phát triển tiếp trong 6 tháng, 12 tháng, 3 năm....
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO để chạy độc lập hoàn toàn.

Chiến trả lời.

## DataTP OKR/KPI/Job Tracking

Nhật trả lời.

## DataTP Document IE

Kế hoạch phát triển tiếp trong 6 tháng, 12 tháng, 3 năm....
- Kế hoach 3 tháng tới là tách dự án, dùng db riêng giao diện độc lập. Có thể kết hợp với Keycloadk SSO và datatp-core để chạy độc lập hoàn toàn.
- Kế hoạch 6 tháng tới là Tích hợp với phần mềm CMS strapi để cung cấp đầy đủ dịch vụ CMS và document management.

Đạt Lương trả lời.

## DataTP Spreadsheet

Đàn + Chiến trả lời.

## Bee Legacy

Đàn trả lời.

## Beesco

Tuấn trả lời.